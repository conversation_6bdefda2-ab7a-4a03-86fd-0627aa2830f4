import { BusinessTypeEnum, BusinessConfigStatusEnum } from '@/types/business/businesscfg/config'
import { BusinessStatusEnum } from '@/types/business/businesscfg/order-relation'

// 业务类型配置
export const BUSINESS_TYPE_CONFIG = {
  [BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE]: {
    name: '数据存证',
    description: '为数据提供时间戳和完整性证明',
    icon: 'ep:document',
    color: '#409EFF',
    defaultProcessingDays: 1,
    features: ['数据完整性验证', '时间戳证明', '法律效力']
  },
  [BusinessTypeEnum.SOFTWARE_REGISTER]: {
    name: '软件著作权登记',
    description: '软件著作权登记申请和审核',
    icon: 'ep:cpu',
    color: '#67C23A',
    defaultProcessingDays: 30,
    features: ['著作权保护', '法律认可', '知识产权证明']
  },
  [BusinessTypeEnum.REAL_IDENTIFY]: {
    name: '实名认证',
    description: '用户身份实名认证服务',
    icon: 'ep:user',
    color: '#E6A23C',
    defaultProcessingDays: 1,
    features: ['身份验证', '实名保障', '安全认证']
  },
  [BusinessTypeEnum.COPYRIGHT_REGISTER]: {
    name: '版权登记',
    description: '作品版权登记申请和审核',
    icon: 'ep:copyright',
    color: '#F56C6C',
    defaultProcessingDays: 15,
    features: ['版权保护', '原创证明', '法律保障']
  },
  [BusinessTypeEnum.BLOCKCHAIN_EVIDENCE]: {
    name: '区块链存证',
    description: '基于区块链技术的数据存证服务',
    icon: 'ep:link',
    color: '#909399',
    defaultProcessingDays: 1,
    features: ['不可篡改', '去中心化', '永久保存']
  }
}

// 业务配置状态配置
export const BUSINESS_CONFIG_STATUS_CONFIG = {
  [BusinessConfigStatusEnum.ACTIVE]: {
    name: '启用',
    color: 'success',
    icon: 'ep:check'
  },
  [BusinessConfigStatusEnum.INACTIVE]: {
    name: '禁用',
    color: 'danger',
    icon: 'ep:close'
  }
}

// 业务状态配置
export const BUSINESS_STATUS_CONFIG = {
  [BusinessStatusEnum.PENDING]: {
    name: '待处理',
    color: 'warning',
    icon: 'ep:clock',
    description: '业务申请已提交，等待处理'
  },
  [BusinessStatusEnum.PROCESSING]: {
    name: '处理中',
    color: 'primary',
    icon: 'ep:loading',
    description: '业务正在处理中'
  },
  [BusinessStatusEnum.COMPLETED]: {
    name: '已完成',
    color: 'success',
    icon: 'ep:check',
    description: '业务处理完成'
  },
  [BusinessStatusEnum.FAILED]: {
    name: '处理失败',
    color: 'danger',
    icon: 'ep:close',
    description: '业务处理失败'
  },
  [BusinessStatusEnum.CANCELLED]: {
    name: '已取消',
    color: 'info',
    icon: 'ep:remove',
    description: '业务已取消'
  }
}

// 会员等级配置
export const MEMBER_LEVEL_CONFIG = {
  bronze: {
    name: '青铜会员',
    color: '#CD7F32',
    icon: 'ep:medal',
    discountRate: 0.9 // 9折
  },
  silver: {
    name: '白银会员',
    color: '#C0C0C0',
    icon: 'ep:medal',
    discountRate: 0.8 // 8折
  },
  gold: {
    name: '黄金会员',
    color: '#FFD700',
    icon: 'ep:medal',
    discountRate: 0.7 // 7折
  }
}

// 表格配置
export const TABLE_CONFIG = {
  pageSize: 20,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper'
}

// 表单验证规则
export const FORM_RULES = {
  businessType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  configName: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  productSpuId: [
    { required: true, message: '请选择关联商品', trigger: 'change' }
  ],
  processingDays: [
    { required: true, message: '请输入处理天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '处理天数必须在 1 到 365 之间', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 操作权限配置
export const PERMISSION_CONFIG = {
  // 业务配置权限
  CONFIG_QUERY: 'business:product-config:query',
  CONFIG_CREATE: 'business:product-config:create',
  CONFIG_UPDATE: 'business:product-config:update',
  CONFIG_DELETE: 'business:product-config:delete',
  CONFIG_SET_DEFAULT: 'business:product-config:set-default',
  
  // 业务订单关联权限
  ORDER_RELATION_QUERY: 'business:order-relation:query',
  ORDER_RELATION_UPDATE: 'business:order-relation:update',
  ORDER_RELATION_EXPORT: 'business:order-relation:export'
}

// 导出配置
export const EXPORT_CONFIG = {
  maxRecords: 10000,
  filename: {
    config: '业务配置数据',
    orderRelation: '业务订单关联数据'
  }
}

export default {
  BUSINESS_TYPE_CONFIG,
  BUSINESS_CONFIG_STATUS_CONFIG,
  BUSINESS_STATUS_CONFIG,
  MEMBER_LEVEL_CONFIG,
  TABLE_CONFIG,
  FORM_RULES,
  PERMISSION_CONFIG,
  EXPORT_CONFIG
}
