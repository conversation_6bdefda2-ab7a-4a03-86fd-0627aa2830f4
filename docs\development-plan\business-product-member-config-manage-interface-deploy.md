﻿# 业务商品配置管理端前端开发计划

## 🎯 项目概述
- **项目名称**：业务商品配置管理端前端
- **技术栈**：Vue 3 + TypeScript + Element Plus + Vite
- **开发周期**：预计 5 个工作日
- **对应后端**：Spring Boot 项目 (wenchuang-chain-manage-service-2.0)
- **需求文档**：`docs/product-requirements/business-product-member-config.md`
- **后端开发计划**：`docs/development-plan/business-product-member-config-deploy.md`

## 📁 前端目录结构规划

```
src/
├── views/
│   └── business/
│       └── config/
│           ├── index.vue                    # 业务商品配置列表页
│           ├── components/
│           │   ├── ConfigForm.vue           # 配置表单组件
│           │   ├── ConfigDetail.vue         # 配置详情组件
│           │   ├── DefaultConfigManager.vue # 默认配置管理组件
│           │   ├── PricePreview.vue         # 价格预览组件
│           │   └── BusinessTypeSelector.vue # 业务类型选择器
│           └── order-relation/
│               ├── index.vue                # 业务订单关联列表页
│               └── components/
│                   ├── OrderDetail.vue     # 订单详情组件
│                   └── StatusManager.vue   # 状态管理组件
├── api/
│   └── business/
│       ├── config.ts                       # 业务配置API
│       └── order-relation.ts               # 业务订单关联API
├── types/
│   └── business/
│       ├── config.ts                       # 业务配置类型定义
│       └── order-relation.ts               # 业务订单关联类型定义
└── utils/
    └── business/
        ├── constants.ts                     # 业务常量定义
        └── helpers.ts                       # 业务辅助函数
```

## 🔧 第一阶段：基础设施和类型定义（预计1天）

### 1.1 API接口定义
- [ ] `src/api/business/businesscfg/config.ts` - 业务配置API接口
- [ ] `src/api/business/businesscfg/order-relation.ts` - 业务订单关联API接口

### 1.2 TypeScript类型定义
- [ ] `src/types/business/businesscfg/config.ts` - 业务配置相关类型
- [ ] `src/types/business/businesscfg/order-relation.ts` - 业务订单关联相关类型

### 1.3 常量和枚举定义
- [ ] `src/utils/business/businesscfg/constants.ts` - 业务常量定义
- [ ] `src/utils/business/businesscfg/helpers.ts` - 业务辅助函数

### 1.4 路由配置
- [ ] 在 `src/router/modules/` 下添加业务配置路由
- [ ] 配置权限控制和菜单显示

**关键类型定义**：
```typescript
// 业务类型枚举
export enum BusinessTypeEnum {
  DATA_CERTIFICATE_EVIDENCE = 'data_certificate_evidence',
  SOFTWARE_REGISTER = 'software_register',
  REAL_IDENTIFY = 'real_identify',
  COPYRIGHT_REGISTER = 'copyright_register',
  BLOCKCHAIN_EVIDENCE = 'blockchain_evidence'
}

// 业务配置状态枚举
export enum BusinessConfigStatusEnum {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

// 业务配置接口
export interface BusinessProductConfig {
  id?: number
  businessType: string
  productSpuId: number
  configName: string
  serviceDescription?: string
  processingDays: number
  isDefault: boolean
  status: string
  sortOrder: number
  createTime?: string
  updateTime?: string
}

// 业务订单关联接口
export interface BusinessOrderRelation {
  id?: number
  orderId: number
  businessType: string
  businessId?: number
  businessProductConfigId: number
  businessStatus: string
  businessData?: string
  processResult?: string
  createTime?: string
  updateTime?: string
}
```

## 🎨 第二阶段：核心组件开发（预计2天）

### 2.1 业务配置管理页面
- [ ] `src/views/business/businesscfg/config/index.vue` - 主列表页面
  - **功能**：配置列表展示、搜索筛选、新增/编辑/删除操作
  - **特色功能**：默认配置标识、价格预览、批量操作
  - **权限**：`business:product-config:*`

### 2.2 核心组件开发
- [ ] `src/views/business/businesscfg/config/components/ConfigForm.vue` - 配置表单组件
  - **功能**：新增/编辑配置表单
  - **特色功能**：默认配置设置、商品选择器、实时价格预览
  - **验证**：默认配置唯一性验证

- [ ] `src/views/business/businesscfg/config/components/DefaultConfigManager.vue` - 默认配置管理
  - **功能**：默认配置切换、防止删除唯一默认配置
  - **业务逻辑**：每个业务类型必须有且仅有一个默认配置

- [ ] `src/views/business/businesscfg/config/components/PricePreview.vue` - 价格预览组件
  - **功能**：实时显示各会员等级价格
  - **展示**：原价、会员折扣价格矩阵

- [ ] `src/views/business/businesscfg/config/components/BusinessTypeSelector.vue` - 业务类型选择器
  - **功能**：业务类型选择、描述展示

### 2.3 业务订单关联管理页面
- [ ] `src/views/business/businesscfg/config/order-relation/index.vue` - 业务订单关联列表
  - **功能**：订单关联查询、状态管理、详情查看
  - **权限**：`business:order-relation:*`

- [ ] `src/views/business/businesscfg/config/order-relation/components/OrderDetail.vue` - 订单详情
  - **功能**：订单详细信息展示、业务数据展示

- [ ] `src/views/business/businesscfg/config/order-relation/components/StatusManager.vue` - 状态管理
  - **功能**：业务状态更新、处理结果录入

## 📊 第三阶段：高级功能和数据展示（预计1天）

### 3.1 价格展示矩阵页面
- [ ] `src/views/business/businesscfg/config/price-matrix.vue` - 价格展示矩阵
  - **功能**：价格对比矩阵、会员等级价格展示
  - **展示维度**：
    - 横轴：会员等级（青铜、白银、黄金）
    - 纵轴：业务商品配置（按业务类型分组）
    - 单元格：自动计算的会员价格
  - **特色功能**：默认配置突出显示、价格导出

### 3.2 数据统计页面
- [ ] `src/views/business/businesscfg/config/statistics.vue` - 数据统计页面
  - **配置统计**：各业务类型商品数量、价格配置完整性检查
  - **订单统计**：各会员等级订单分布、会员折扣使用情况
  - **收入分析**：收入分析报表、会员等级转化分析

### 3.3 服务等级对比组件
- [ ] `src/views/business/businesscfg/config/components/ServiceComparison.vue` - 服务等级对比
  - **功能**：同一业务类型的不同配置对比
  - **对比维度**：价格、处理周期、服务内容对比
  - **展示**：表格对比、推荐标识

## 🔌 第四阶段：API集成和数据流（预计0.5天）

### 4.1 API接口实现
- [ ] 实现所有业务配置管理API调用
- [ ] 实现业务订单关联API调用
- [ ] 实现商品价格查询API集成
- [ ] 实现会员等级信息API集成

### 4.2 数据流管理
- [ ] 配置数据的增删改查
- [ ] 默认配置业务逻辑处理
- [ ] 价格信息实时获取和计算
- [ ] 订单关联数据管理

**API接口示例**：
```typescript
// 业务配置API
export const businessConfigApi = {
  // 分页查询配置
  getConfigPage: (params: BusinessConfigPageReq) =>
    request.get('/admin-api/business/businesscfg/product-config/page', { params }),

  // 创建配置
  createConfig: (data: BusinessConfigCreateReq) =>
    request.post('/admin-api/business/businesscfg/product-config', data),

  // 更新配置
  updateConfig: (id: number, data: BusinessConfigUpdateReq) =>
    request.put(`/admin-api/business/businesscfg/product-config/${id}`, data),

  // 删除配置
  deleteConfig: (id: number) =>
    request.delete(`/admin-api/business/businesscfg/product-config/${id}`),

  // 设置默认配置
  setDefaultConfig: (id: number) =>
    request.put(`/admin-api/business/businesscfg/product-config/${id}/set-default`),

  // 按业务类型查询
  getConfigsByType: (businessType: string) =>
    request.get('/admin-api/business/businesscfg/product-config/list-by-type', {
      params: { businessType }
    })
}
```

## 🧪 第五阶段：测试和优化（预计0.5天）

### 5.1 功能测试
- [ ] 配置管理功能测试
- [ ] 默认配置业务逻辑测试
- [ ] 价格预览功能测试
- [ ] 订单关联管理测试

### 5.2 用户体验优化
- [ ] 加载状态优化
- [ ] 错误处理和提示优化
- [ ] 响应式布局适配
- [ ] 操作流程优化

### 5.3 性能优化
- [ ] 列表分页和搜索优化
- [ ] 价格计算缓存优化
- [ ] 组件懒加载
- [ ] API请求优化

## 📋 页面功能清单

### 业务配置管理页面功能
- [x] **配置列表展示**：分页列表、搜索筛选
- [x] **默认配置标识**：清晰标识默认配置
- [x] **新增配置**：表单验证、默认配置自动设置
- [x] **编辑配置**：在线编辑、实时预览
- [x] **删除配置**：删除保护（防止删除唯一默认配置）
- [x] **默认配置管理**：设置默认、切换默认
- [x] **价格预览**：实时显示各会员等级价格
- [x] **批量操作**：批量启用/禁用、批量删除
- [x] **排序管理**：拖拽排序、手动排序

### 业务订单关联管理页面功能
- [x] **订单关联列表**：分页查询、多维度筛选
- [x] **订单详情查看**：完整订单信息展示
- [x] **业务状态管理**：状态更新、处理结果录入
- [x] **业务数据展示**：JSON格式数据美化展示
- [x] **处理进度跟踪**：状态流转记录

### 价格展示矩阵页面功能
- [x] **价格对比矩阵**：会员等级 × 业务配置价格矩阵
- [x] **默认配置突出**：默认配置特殊标识
- [x] **价格导出**：Excel格式价格表导出
- [x] **服务等级对比**：同业务类型不同配置对比

### 数据统计页面功能
- [x] **配置统计**：各业务类型配置数量统计
- [x] **订单统计**：订单分布、会员折扣使用统计
- [x] **收入分析**：收入趋势、会员等级转化分析
- [x] **完整性检查**：配置完整性检查报告

## 🔐 权限控制

### 菜单权限
```typescript
// 路由权限配置
export const businessConfigRoutes = [
  {
    path: '/business/businesscfg/config',
    component: Layout,
    meta: {
      title: '业务配置管理',
      icon: 'setting',
      roles: ['admin', 'business-manager']
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/business/businesscfg/config/index.vue'),
        meta: {
          title: '配置管理',
          permission: 'business:product-config:query'
        }
      },
      {
        path: 'order-relation',
        component: () => import('@/views/business/businesscfg/config/order-relation/index.vue'),
        meta: {
          title: '订单关联',
          permission: 'business:order-relation:query'
        }
      },
      {
        path: 'price-matrix',
        component: () => import('@/views/business/businesscfg/config/price-matrix.vue'),
        meta: {
          title: '价格矩阵',
          permission: 'business:product-config:query'
        }
      },
      {
        path: 'statistics',
        component: () => import('@/views/business/businesscfg/config/statistics.vue'),
        meta: {
          title: '数据统计',
          permission: 'business:product-config:query'
        }
      }
    ]
  }
]
```

### 操作权限
```typescript
// 按钮权限控制
export const businessConfigPermissions = {
  // 配置管理权限
  'business:product-config:create': '新增配置',
  'business:product-config:update': '编辑配置',
  'business:product-config:delete': '删除配置',
  'business:product-config:query': '查询配置',
  'business:product-config:set-default': '设置默认配置',

  // 订单关联权限
  'business:order-relation:query': '查询订单关联',
  'business:order-relation:update': '更新业务状态'
}
```

## 📊 时间安排
- **总计**：5个工作日
- **第1天**：基础设施和类型定义
- **第2-3天**：核心组件开发（配置管理、订单关联）
- **第4天**：高级功能开发（价格矩阵、数据统计）
- **第5天**：API集成、测试和优化

## ✅ 成功标准

### 功能指标
- 支持5种业务类型的商品配置管理
- **默认配置机制**：每个业务类型有且仅有一个默认配置
- **多级服务支持**：支持基础版、标准版、高级版等多种配置
- 配置管理响应时间<200ms
- 价格预览实时计算和展示
- 与后端API无缝集成
- 业务订单与配置统一管理

### 业务指标
- 业务商品配置覆盖率100%
- **默认配置完整性100%**（每个业务类型都有默认配置）
- 价格计算准确率100%
- 价格数据一致性100%（统一价格源）
- 系统可用性99.9%
- 开发周期缩短30%（相比独立价格管理方案）
- 配置管理效率提升50%

### 用户体验指标
- 页面加载时间<2秒
- 操作响应时间<500ms
- 界面操作直观性评分>4.5/5
- 错误处理完善性>95%

---

**文档版本**：v1.0（管理端前端开发计划）
**创建日期**：2025-01-15
**最后更新**：2025-01-15
**文档状态**：已完成，可执行开发
**对应后端版本**：v2.0（需求确认版）
