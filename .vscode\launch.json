{"version": "0.2.0", "configurations": [{"type": "msedge", "request": "launch", "name": "调试：本地开发环境 (3000端口)", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/edge-debug", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor", "--remote-debugging-port=9222", "--no-first-run", "--no-default-browser-check"], "timeout": 30000, "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"type": "chrome", "request": "launch", "name": "调试：本地开发环境 - Chrome (3000端口)", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor", "--remote-debugging-port=9223", "--no-first-run", "--no-default-browser-check"], "timeout": 30000, "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"type": "msedge", "request": "launch", "name": "调试：开发服务器环境 (3000端口)", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/edge-debug-dev", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor", "--remote-debugging-port=9224", "--no-first-run", "--no-default-browser-check"], "timeout": 30000, "skipFiles": ["<node_internals>/**", "node_modules/**"], "env": {"NODE_ENV": "development"}}, {"type": "msedge", "request": "launch", "name": "调试：生产环境 (80端口)", "url": "http://localhost", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": false}, {"type": "msedge", "request": "attach", "name": "附加到现有浏览器", "port": 9222, "webRoot": "${workspaceFolder}/src", "sourceMaps": true}, {"type": "msedge", "request": "launch", "name": "快速调试：连接到3000端口", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/edge-quick-debug", "runtimeArgs": ["--disable-web-security", "--remote-debugging-port=9225", "--no-first-run"], "timeout": 10000, "skipFiles": ["<node_internals>/**", "node_modules/**"]}], "compounds": [{"name": "启动并调试：本地开发环境", "configurations": ["调试：本地开发环境 (3000端口)"], "stopAll": true, "presentation": {"hidden": false, "group": "debug", "order": 1}}, {"name": "启动并调试：开发服务器环境", "configurations": ["调试：开发服务器环境 (3000端口)"], "stopAll": true, "presentation": {"hidden": false, "group": "debug", "order": 2}}]}