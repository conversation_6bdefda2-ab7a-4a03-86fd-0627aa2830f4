<template>
  <Dialog
    v-model="dialogVisible"
    title="状态管理"
    width="600px"
    @close="handleClose"
  >
    <div v-if="orderData" class="status-manager-container">
      <!-- 当前状态 -->
      <div class="current-status">
        <h3 class="section-title">当前状态</h3>
        <div class="status-display">
          <div class="status-icon" :style="{ backgroundColor: currentStatusConfig?.color }">
            <Icon :icon="currentStatusConfig?.icon" />
          </div>
          <div class="status-info">
            <div class="status-name">{{ currentStatusConfig?.name }}</div>
            <div class="status-description">{{ currentStatusConfig?.description }}</div>
          </div>
        </div>
      </div>

      <!-- 状态更新表单 -->
      <div class="status-form">
        <h3 class="section-title">更新状态</h3>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="新状态" prop="businessStatus">
            <el-select
              v-model="formData.businessStatus"
              placeholder="请选择新状态"
              style="width: 100%"
              @change="handleStatusChange"
            >
              <el-option
                v-for="option in availableStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              >
                <div class="flex items-center justify-between w-full">
                  <div class="flex items-center">
                    <Icon :icon="option.icon" class="mr-2" />
                    <span>{{ option.label }}</span>
                  </div>
                  <el-tag v-if="option.disabled" type="info" size="small">
                    不可选
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="requiresResult"
            label="处理结果"
            prop="processResult"
          >
            <el-input
              v-model="formData.processResult"
              type="textarea"
              :rows="4"
              placeholder="请输入处理结果"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item
            v-if="selectedStatusConfig"
            label="状态说明"
          >
            <div class="status-help">
              <Icon :icon="selectedStatusConfig.icon" class="mr-2" />
              <span>{{ selectedStatusConfig.description }}</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 状态流转图 -->
      <div class="status-flow">
        <h3 class="section-title">状态流转</h3>
        <div class="flow-diagram">
          <div
            v-for="(status, index) in statusFlow"
            :key="status.value"
            class="flow-item"
            :class="{ 
              active: status.value === orderData.businessStatus,
              completed: isStatusCompleted(status.value),
              next: status.value === formData.businessStatus
            }"
          >
            <div class="flow-icon">
              <Icon :icon="status.icon" />
            </div>
            <div class="flow-label">{{ status.label }}</div>
            <div v-if="index < statusFlow.length - 1" class="flow-arrow">
              <Icon icon="ep:arrow-right" />
            </div>
          </div>
        </div>
      </div>

      <!-- 操作历史 -->
      <div class="operation-history">
        <h3 class="section-title">操作历史</h3>
        <el-timeline size="small">
          <el-timeline-item
            v-for="(history, index) in statusHistory"
            :key="index"
            :timestamp="history.time"
            :type="history.type"
          >
            <div class="history-content">
              <div class="history-title">
                状态变更：{{ history.fromStatus }} → {{ history.toStatus }}
              </div>
              <div v-if="history.result" class="history-result">
                处理结果：{{ history.result }}
              </div>
              <div class="history-operator">操作人：{{ history.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        确定更新
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts" name="StatusManager">
import { BusinessOrderRelationApi } from '@/api/business/businesscfg/order-relation'
import { BusinessOrderRelationTableItem, BusinessOrderRelationUpdateForm } from '@/types/business/businesscfg/order-relation'
import {
  getBusinessStatusName,
  getBusinessStatusConfig,
  getBusinessStatusOptions
} from '@/utils/business/businesscfg/helpers'
import { BUSINESS_STATUS_CONFIG } from '@/utils/business/businesscfg/constants'

// 定义事件
const emit = defineEmits(['success'])

// 响应式数据
const dialogVisible = ref(false)
const submitLoading = ref(false)
const orderData = ref<BusinessOrderRelationTableItem | null>(null)

// 表单数据
const formData = reactive<BusinessOrderRelationUpdateForm>({
  id: 0,
  businessStatus: '',
  processResult: ''
})

// 表单引用
const formRef = ref()

// 状态流转定义
const statusFlow = [
  { value: 'PENDING', label: '待处理', icon: 'ep:clock' },
  { value: 'PROCESSING', label: '处理中', icon: 'ep:loading' },
  { value: 'COMPLETED', label: '已完成', icon: 'ep:check' },
  { value: 'FAILED', label: '处理失败', icon: 'ep:close' }
]

// 状态历史
const statusHistory = ref([
  {
    time: '2024-01-15 10:00:00',
    type: 'primary',
    fromStatus: '待处理',
    toStatus: '处理中',
    result: '',
    operator: '管理员'
  }
])

// 表单验证规则
const formRules = {
  businessStatus: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ],
  processResult: [
    { required: true, message: '请输入处理结果', trigger: 'blur' },
    { min: 10, max: 1000, message: '处理结果长度在 10 到 1000 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const currentStatusConfig = computed(() => {
  if (!orderData.value) return null
  return getBusinessStatusConfig(orderData.value.businessStatus)
})

const selectedStatusConfig = computed(() => {
  if (!formData.businessStatus) return null
  return getBusinessStatusConfig(formData.businessStatus)
})

const requiresResult = computed(() => {
  return formData.businessStatus === 'COMPLETED' || formData.businessStatus === 'FAILED'
})

const availableStatusOptions = computed(() => {
  if (!orderData.value) return []
  
  const currentStatus = orderData.value.businessStatus
  const allOptions = getBusinessStatusOptions()
  
  // 定义状态流转规则
  const statusTransitions: Record<string, string[]> = {
    'PENDING': ['PROCESSING', 'CANCELLED'],
    'PROCESSING': ['COMPLETED', 'FAILED', 'CANCELLED'],
    'COMPLETED': [], // 已完成不能再变更
    'FAILED': ['PROCESSING'], // 失败可以重新处理
    'CANCELLED': [] // 已取消不能再变更
  }
  
  const allowedStatuses = statusTransitions[currentStatus] || []
  
  return allOptions.map(option => ({
    ...option,
    icon: getBusinessStatusConfig(option.value)?.icon || 'ep:circle',
    disabled: !allowedStatuses.includes(option.value) || option.value === currentStatus
  }))
})

// 判断状态是否已完成
const isStatusCompleted = (status: string) => {
  if (!orderData.value) return false
  const currentIndex = statusFlow.findIndex(s => s.value === orderData.value!.businessStatus)
  const targetIndex = statusFlow.findIndex(s => s.value === status)
  return targetIndex < currentIndex
}

// 打开弹窗
const open = (data: BusinessOrderRelationTableItem) => {
  orderData.value = data
  dialogVisible.value = true
  
  // 重置表单
  resetForm()
  
  // 加载状态历史
  loadStatusHistory(data.id)
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: orderData.value?.id || 0,
    businessStatus: '',
    processResult: ''
  })
  
  formRef.value?.clearValidate()
}

// 加载状态历史
const loadStatusHistory = async (id: number) => {
  try {
    // 这里应该调用API获取状态历史
    // 暂时使用模拟数据
    console.log('加载状态历史:', id)
  } catch (error) {
    console.error('加载状态历史失败:', error)
  }
}

// 状态变化处理
const handleStatusChange = (status: string) => {
  // 根据状态设置是否需要处理结果
  if (!requiresResult.value) {
    formData.processResult = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  const statusName = getBusinessStatusName(formData.businessStatus)
  await ElMessageBox.confirm(`确定要将状态更新为"${statusName}"吗？`, '确认操作', {
    type: 'warning'
  })
  
  submitLoading.value = true
  try {
    await BusinessOrderRelationApi.updateOrderRelation(formData)
    ElMessage.success('状态更新成功')
    
    handleClose()
    emit('success')
  } catch (error) {
    console.error('状态更新失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  orderData.value = null
  resetForm()
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.status-manager-container {
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e8ed;
}

.current-status {
  margin-bottom: 24px;
}

.status-display {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-right: 16px;
}

.status-info {
  flex: 1;
}

.status-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.status-description {
  color: #666;
  font-size: 14px;
}

.status-form {
  margin-bottom: 24px;
}

.status-help {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
  background: #f0f9ff;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.status-flow {
  margin-bottom: 24px;
}

.flow-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.flow-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.flow-item.active {
  opacity: 1;
  color: #409eff;
}

.flow-item.completed {
  opacity: 1;
  color: #67c23a;
}

.flow-item.next {
  opacity: 1;
  color: #e6a23c;
}

.flow-icon {
  width: 40px;
  height: 40px;
  border: 2px solid currentColor;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-bottom: 8px;
  background: white;
}

.flow-label {
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
}

.flow-arrow {
  position: absolute;
  right: -20px;
  top: 20px;
  font-size: 16px;
  color: #ddd;
}

.operation-history {
  margin-bottom: 24px;
}

.history-content {
  padding: 8px 0;
}

.history-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.history-result {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.history-operator {
  color: #999;
  font-size: 12px;
}
</style>
