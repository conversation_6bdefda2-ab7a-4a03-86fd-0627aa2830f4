<template>
  <Dialog
    v-model="dialogVisible"
    title="价格预览"
    width="600px"
    @close="handleClose"
  >
    <div v-if="configData" class="price-preview-container">
      <!-- 配置信息 -->
      <div class="config-info">
        <h3 class="section-title">配置信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">业务类型：</span>
            <div class="value">
              <Icon :icon="getBusinessTypeConfig(configData.businessType)?.icon" class="mr-2" />
              {{ getBusinessTypeName(configData.businessType) }}
            </div>
          </div>
          <div class="info-item">
            <span class="label">配置名称：</span>
            <div class="value">
              {{ configData.configName }}
              <el-tag v-if="configData.isDefault" type="primary" size="small" class="ml-2">
                默认
              </el-tag>
            </div>
          </div>
          <div class="info-item">
            <span class="label">处理天数：</span>
            <div class="value">{{ configData.processingDays }}天</div>
          </div>
          <div class="info-item">
            <span class="label">服务描述：</span>
            <div class="value">{{ configData.serviceDescription || '暂无描述' }}</div>
          </div>
        </div>
      </div>

      <!-- 价格信息 -->
      <div class="price-info">
        <h3 class="section-title">价格信息</h3>
        
        <!-- 原价 -->
        <div class="original-price">
          <div class="price-label">原价</div>
          <div class="price-value original">
            {{ formatPrice(priceData.originalPrice) }}
          </div>
        </div>

        <!-- 会员价格 -->
        <div class="member-prices">
          <div class="price-label">会员价格</div>
          <div class="member-price-grid">
            <div
              v-for="(price, level) in priceData.memberPrices"
              :key="level"
              class="member-price-card"
            >
              <div class="member-level">
                <Icon :icon="getMemberLevelConfig(level)?.icon" class="level-icon" />
                <span class="level-name">{{ getMemberLevelName(level) }}</span>
              </div>
              <div class="member-price">{{ formatPrice(price) }}</div>
              <div class="discount-info">
                <span class="discount-rate">{{ formatDiscount(priceData.discountRates[level]) }}</span>
                <span class="saved-amount">省{{ formatPrice(priceData.originalPrice - price) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格对比表格 -->
        <div class="price-comparison">
          <div class="price-label">价格对比</div>
          <el-table :data="comparisonData" border size="small">
            <el-table-column label="会员等级" align="center">
              <template #default="{ row }">
                <div class="flex items-center justify-center">
                  <Icon :icon="row.icon" class="mr-2" />
                  <span>{{ row.levelName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="折扣率" prop="discountRate" align="center">
              <template #default="{ row }">
                <el-tag :color="row.color" size="small">
                  {{ row.discountRate }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="会员价格" prop="memberPrice" align="center">
              <template #default="{ row }">
                <span class="font-semibold text-red-500">{{ row.memberPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column label="节省金额" prop="savedAmount" align="center">
              <template #default="{ row }">
                <span class="font-semibold text-green-500">{{ row.savedAmount }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 价格说明 -->
        <div class="price-notes">
          <div class="price-label">价格说明</div>
          <ul class="notes-list">
            <li>价格单位为人民币（元），已包含税费</li>
            <li>会员价格根据会员等级自动计算折扣</li>
            <li>实际支付价格以订单结算时为准</li>
            <li>价格可能会根据市场情况进行调整</li>
          </ul>
        </div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExport">
        <Icon icon="ep:download" class="mr-1" />
        导出价格表
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts" name="PricePreview">
import { BusinessConfigTableItem } from '@/types/business/businesscfg/config'
import { PricePreviewData } from '@/types/business/businesscfg/config'
import {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getMemberLevelName,
  formatPrice,
  formatDiscount,
  calculateMemberPrice,
  calculateDiscountRate
} from '@/utils/business/businesscfg/helpers'
import { MEMBER_LEVEL_CONFIG } from '@/utils/business/businesscfg/constants'

// 响应式数据
const dialogVisible = ref(false)
const configData = ref<BusinessConfigTableItem | null>(null)
const priceData = ref<PricePreviewData>({
  originalPrice: 0,
  memberPrices: {
    bronze: 0,
    silver: 0,
    gold: 0
  },
  discountRates: {
    bronze: 0,
    silver: 0,
    gold: 0
  }
})

// 计算属性
const comparisonData = computed(() => {
  if (!priceData.value) return []
  
  return Object.entries(MEMBER_LEVEL_CONFIG).map(([level, config]) => ({
    level,
    levelName: config.name,
    icon: config.icon,
    color: config.color,
    discountRate: formatDiscount(priceData.value.discountRates[level as keyof typeof priceData.value.discountRates]),
    memberPrice: formatPrice(priceData.value.memberPrices[level as keyof typeof priceData.value.memberPrices]),
    savedAmount: formatPrice(priceData.value.originalPrice - priceData.value.memberPrices[level as keyof typeof priceData.value.memberPrices])
  }))
})

// 获取会员等级配置
const getMemberLevelConfig = (level: string) => {
  return MEMBER_LEVEL_CONFIG[level as keyof typeof MEMBER_LEVEL_CONFIG]
}

// 打开弹窗
const open = async (config: BusinessConfigTableItem) => {
  configData.value = config
  dialogVisible.value = true
  
  // 加载价格数据
  await loadPriceData(config)
}

// 加载价格数据
const loadPriceData = async (config: BusinessConfigTableItem) => {
  try {
    // 这里应该调用API获取商品价格信息
    // 暂时模拟数据
    const originalPrice = 10000 // 100.00元
    
    priceData.value = {
      originalPrice,
      memberPrices: {
        bronze: calculateMemberPrice(originalPrice, 'bronze'),
        silver: calculateMemberPrice(originalPrice, 'silver'),
        gold: calculateMemberPrice(originalPrice, 'gold')
      },
      discountRates: {
        bronze: calculateDiscountRate(originalPrice, calculateMemberPrice(originalPrice, 'bronze')),
        silver: calculateDiscountRate(originalPrice, calculateMemberPrice(originalPrice, 'silver')),
        gold: calculateDiscountRate(originalPrice, calculateMemberPrice(originalPrice, 'gold'))
      }
    }
  } catch (error) {
    console.error('加载价格数据失败:', error)
  }
}

// 导出价格表
const handleExport = () => {
  if (!configData.value) return
  
  // 构建导出数据
  const exportData = {
    configInfo: {
      businessType: getBusinessTypeName(configData.value.businessType),
      configName: configData.value.configName,
      processingDays: configData.value.processingDays,
      serviceDescription: configData.value.serviceDescription
    },
    priceInfo: {
      originalPrice: formatPrice(priceData.value.originalPrice),
      memberPrices: Object.entries(priceData.value.memberPrices).map(([level, price]) => ({
        level: getMemberLevelName(level),
        price: formatPrice(price),
        discount: formatDiscount(priceData.value.discountRates[level as keyof typeof priceData.value.discountRates]),
        saved: formatPrice(priceData.value.originalPrice - price)
      }))
    }
  }
  
  // 这里可以实现具体的导出逻辑（Excel、PDF等）
  console.log('导出数据:', exportData)
  ElMessage.success('价格表导出成功')
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  configData.value = null
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.price-preview-container {
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e8ed;
}

.config-info {
  margin-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 8px;
}

.value {
  flex: 1;
  display: flex;
  align-items: center;
}

.price-info {
  margin-bottom: 24px;
}

.price-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.original-price {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.price-value.original {
  font-size: 24px;
  font-weight: 700;
  color: #e74c3c;
}

.member-price-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.member-price-card {
  background: #fff;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.member-price-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.member-level {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.level-icon {
  font-size: 18px;
  margin-right: 4px;
}

.level-name {
  font-weight: 500;
  color: #333;
}

.member-price {
  font-size: 20px;
  font-weight: 700;
  color: #e74c3c;
  margin-bottom: 8px;
}

.discount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discount-rate {
  background: #e8f5e8;
  color: #27ae60;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.saved-amount {
  color: #27ae60;
  font-size: 12px;
  font-weight: 500;
}

.price-comparison {
  margin-bottom: 20px;
}

.price-notes {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.notes-list {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.notes-list li {
  margin-bottom: 4px;
  font-size: 14px;
}
</style>
