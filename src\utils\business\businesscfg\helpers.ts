import { BusinessTypeEnum, BusinessConfigStatusEnum } from '@/types/business/businesscfg/config'
import { BusinessStatusEnum } from '@/types/business/businesscfg/order-relation'
import { BUSINESS_TYPE_CONFIG, BUSINESS_CONFIG_STATUS_CONFIG, BUSINESS_STATUS_CONFIG, MEMBER_LEVEL_CONFIG } from './constants'

// 创建中文名称到英文代码的反向映射
const BUSINESS_TYPE_NAME_TO_CODE: Record<string, BusinessTypeEnum> = {
  '数据存证': BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE,
  '数据知识产权存证': BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE,
  '软件著作权登记': BusinessTypeEnum.SOFTWARE_REGISTER,
  '实名认证': BusinessTypeEnum.REAL_IDENTIFY,
  '版权登记': BusinessTypeEnum.COPYRIGHT_REGISTER,
  '作品著作权登记': BusinessTypeEnum.COPYRIGHT_REGISTER,
  '区块链存证': BusinessTypeEnum.BLOCKCHAIN_EVIDENCE
}

// 创建状态中文名称到英文代码的反向映射
const BUSINESS_CONFIG_STATUS_NAME_TO_CODE: Record<string, BusinessConfigStatusEnum> = {
  '启用': BusinessConfigStatusEnum.ACTIVE,
  '禁用': BusinessConfigStatusEnum.INACTIVE,
  '激活': BusinessConfigStatusEnum.ACTIVE,
  '停用': BusinessConfigStatusEnum.INACTIVE
}

/**
 * 将业务类型名称转换为代码
 */
function getBusinessTypeCode(businessType: string): BusinessTypeEnum | string {
  // 如果已经是代码格式，直接返回
  if (Object.values(BusinessTypeEnum).includes(businessType as BusinessTypeEnum)) {
    return businessType as BusinessTypeEnum
  }
  // 如果是中文名称，转换为代码
  return BUSINESS_TYPE_NAME_TO_CODE[businessType] || businessType
}

/**
 * 将业务配置状态名称转换为代码
 */
function getBusinessConfigStatusCode(status: string): BusinessConfigStatusEnum | string {
  // 如果已经是代码格式，直接返回
  if (Object.values(BusinessConfigStatusEnum).includes(status as BusinessConfigStatusEnum)) {
    return status as BusinessConfigStatusEnum
  }
  // 如果是中文名称，转换为代码
  return BUSINESS_CONFIG_STATUS_NAME_TO_CODE[status] || status
}

/**
 * 获取业务类型显示名称
 */
export function getBusinessTypeName(businessType: string): string {
  const code = getBusinessTypeCode(businessType)
  return BUSINESS_TYPE_CONFIG[code as BusinessTypeEnum]?.name || businessType
}

/**
 * 获取业务类型配置
 */
export function getBusinessTypeConfig(businessType: string) {
  const code = getBusinessTypeCode(businessType)
  return BUSINESS_TYPE_CONFIG[code as BusinessTypeEnum]
}

/**
 * 获取业务配置状态显示名称
 */
export function getBusinessConfigStatusName(status: string): string {
  const code = getBusinessConfigStatusCode(status)
  return BUSINESS_CONFIG_STATUS_CONFIG[code as BusinessConfigStatusEnum]?.name || status
}

/**
 * 获取业务配置状态配置
 */
export function getBusinessConfigStatusConfig(status: string) {
  const code = getBusinessConfigStatusCode(status)
  return BUSINESS_CONFIG_STATUS_CONFIG[code as BusinessConfigStatusEnum]
}

/**
 * 获取业务状态显示名称
 */
export function getBusinessStatusName(status: string): string {
  return BUSINESS_STATUS_CONFIG[status as BusinessStatusEnum]?.name || status
}

/**
 * 获取业务状态配置
 */
export function getBusinessStatusConfig(status: string) {
  return BUSINESS_STATUS_CONFIG[status as BusinessStatusEnum]
}

/**
 * 获取会员等级显示名称
 */
export function getMemberLevelName(level: string): string {
  return MEMBER_LEVEL_CONFIG[level as keyof typeof MEMBER_LEVEL_CONFIG]?.name || level
}

/**
 * 计算会员价格
 */
export function calculateMemberPrice(originalPrice: number, level: string): number {
  const config = MEMBER_LEVEL_CONFIG[level as keyof typeof MEMBER_LEVEL_CONFIG]
  if (!config) return originalPrice
  return Math.round(originalPrice * config.discountRate)
}

/**
 * 计算折扣率
 */
export function calculateDiscountRate(originalPrice: number, memberPrice: number): number {
  if (originalPrice <= 0) return 0
  return Math.round((memberPrice / originalPrice) * 10) / 10
}

/**
 * 格式化价格显示
 */
export function formatPrice(price: number): string {
  return `¥${(price / 100).toFixed(2)}`
}

/**
 * 格式化折扣显示
 */
export function formatDiscount(rate: number): string {
  return `${(rate * 10).toFixed(1)}折`
}

/**
 * 获取业务类型选项列表
 */
export function getBusinessTypeOptions() {
  return Object.entries(BUSINESS_TYPE_CONFIG).map(([value, config]) => ({
    value,
    label: config.name,
    description: config.description,
    icon: config.icon
  }))
}

/**
 * 获取业务配置状态选项列表
 */
export function getBusinessConfigStatusOptions() {
  return Object.entries(BUSINESS_CONFIG_STATUS_CONFIG).map(([value, config]) => ({
    value,
    label: config.name,
    color: config.color
  }))
}

/**
 * 获取业务状态选项列表
 */
export function getBusinessStatusOptions() {
  return Object.entries(BUSINESS_STATUS_CONFIG).map(([value, config]) => ({
    value,
    label: config.name,
    color: config.color,
    description: config.description
  }))
}

/**
 * 验证是否可以设置为默认配置
 */
export function canSetAsDefault(configs: any[], targetId: number, businessType: string): boolean {
  // 检查是否已经是默认配置
  const target = configs.find(c => c.id === targetId)
  if (target?.isDefault) return false
  
  // 检查是否同业务类型
  return target?.businessType === businessType
}

/**
 * 验证是否可以删除配置
 */
export function canDeleteConfig(config: any): { canDelete: boolean; reason?: string } {
  // 不能删除默认配置
  if (config.isDefault) {
    return { canDelete: false, reason: '不能删除默认配置' }
  }
  
  // 可以删除
  return { canDelete: true }
}

/**
 * 生成配置排序值
 */
export function generateSortOrder(configs: any[]): number {
  if (!configs || configs.length === 0) return 1
  const maxSort = Math.max(...configs.map(c => c.sortOrder || 0))
  return maxSort + 1
}

/**
 * 格式化业务数据显示
 */
export function formatBusinessData(data: string | object): any[] {
  try {
    const parsed = typeof data === 'string' ? JSON.parse(data) : data
    if (!parsed || typeof parsed !== 'object') return []
    
    return Object.entries(parsed).map(([key, value]) => ({
      key,
      label: key,
      value,
      type: getDataType(value)
    }))
  } catch (error) {
    return [{ key: 'raw', label: '原始数据', value: data, type: 'text' }]
  }
}

/**
 * 获取数据类型
 */
function getDataType(value: any): string {
  if (value === null || value === undefined) return 'text'
  if (typeof value === 'number') return 'number'
  if (typeof value === 'boolean') return 'text'
  if (value instanceof Date) return 'date'
  if (typeof value === 'string') {
    // 检查是否是日期格式
    if (/^\d{4}-\d{2}-\d{2}/.test(value)) return 'date'
    // 检查是否是文件URL
    if (/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(value)) return 'image'
    if (/\.(pdf|doc|docx|xls|xlsx|txt)$/i.test(value)) return 'file'
    return 'text'
  }
  if (typeof value === 'object') return 'json'
  return 'text'
}

/**
 * 获取状态流转历史
 */
export function getStatusHistory(orderRelation: any): any[] {
  // 这里应该从后端获取状态流转历史
  // 暂时模拟数据
  return [
    {
      status: BusinessStatusEnum.PENDING,
      statusName: getBusinessStatusName(BusinessStatusEnum.PENDING),
      time: orderRelation.createTime,
      operator: '系统',
      remark: '订单创建，等待处理'
    }
  ]
}

/**
 * 计算预计完成时间
 */
export function calculateEstimatedCompletionTime(createTime: string, processingDays: number): string {
  const create = new Date(createTime)
  const estimated = new Date(create.getTime() + processingDays * 24 * 60 * 60 * 1000)
  return estimated.toISOString()
}

export default {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getBusinessConfigStatusName,
  getBusinessConfigStatusConfig,
  getBusinessStatusName,
  getBusinessStatusConfig,
  getMemberLevelName,
  calculateMemberPrice,
  calculateDiscountRate,
  formatPrice,
  formatDiscount,
  getBusinessTypeOptions,
  getBusinessConfigStatusOptions,
  getBusinessStatusOptions,
  canSetAsDefault,
  canDeleteConfig,
  generateSortOrder,
  formatBusinessData,
  getStatusHistory,
  calculateEstimatedCompletionTime
}
