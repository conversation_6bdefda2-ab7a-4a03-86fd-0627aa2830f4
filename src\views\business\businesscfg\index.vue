<template>
  <ContentWrap>
    <!-- 搜索表单 -->
    <el-form
      ref="queryFormRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
      class="-mb-15px"
    >
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            v-model="queryParams.businessType"
            placeholder="请选择业务类型"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="option in businessTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
              <div class="flex items-center">
                <Icon :icon="option.icon" class="mr-2" />
                <span>{{ option.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="配置名称" prop="configName">
          <el-input
            v-model="queryParams.configName"
            placeholder="请输入配置名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="默认配置" prop="isDefault">
          <el-select
            v-model="queryParams.isDefault"
            placeholder="请选择"
            clearable
            style="width: 120px"
          >
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-1" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-1" />
            重置
          </el-button>
        </el-form-item>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <!-- 操作按钮 -->
      <el-button
        type="primary"
        @click="handleAdd"
        v-hasPermi="['business:product-config:create']"
      >
        <Icon icon="ep:plus" class="mr-1" />
        新增配置
      </el-button>
      
      <el-button
        type="success"
        :disabled="!selectedIds.length"
        @click="handleBatchEnable"
        v-hasPermi="['business:product-config:update']"
      >
        <Icon icon="ep:check" class="mr-1" />
        批量启用
      </el-button>
      
      <el-button
        type="warning"
        :disabled="!selectedIds.length"
        @click="handleBatchDisable"
        v-hasPermi="['business:product-config:update']"
      >
        <Icon icon="ep:close" class="mr-1" />
        批量禁用
      </el-button>
      
      <el-button
        type="danger"
        :disabled="!selectedIds.length"
        @click="handleBatchDelete"
        v-hasPermi="['business:product-config:delete']"
      >
        <Icon icon="ep:delete" class="mr-1" />
        批量删除
      </el-button>
    </ContentWrap>

    <!-- 数据表格 -->
    <ContentWrap>
      <el-table
      ref="tableRef"
      v-loading="loading"
      :data="configList"
      row-key="id"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      
      <el-table-column
        label="业务类型"
        prop="businessType"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <Icon :icon="getBusinessTypeConfig(row.businessType)?.icon" class="mr-2" />
            <span>{{ getBusinessTypeName(row.businessType) }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="配置名称"
        prop="configName"
        min-width="150"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="flex items-center">
            <span>{{ row.configName }}</span>
            <el-tag
              v-if="row.isDefault"
              type="primary"
              size="small"
              class="ml-2"
            >
              默认
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="关联商品ID"
        prop="productSpuId"
        width="120"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag type="info" size="small">
            {{ row.productSpuId }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        label="服务描述"
        prop="serviceDescription"
        min-width="200"
        show-overflow-tooltip
      />
      
      <el-table-column
        label="处理天数"
        prop="processingDays"
        width="100"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag type="info" size="small">
            {{ row.processingDays }}天
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        label="价格预览"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="handlePricePreview(row)"
          >
            查看价格
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column
        label="状态"
        prop="status"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getBusinessConfigStatusConfig(row.status)?.color as any"
            size="small"
          >
            {{ getBusinessConfigStatusName(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        label="排序"
        prop="sortOrder"
        width="80"
        align="center"
        sortable="custom"
      />
      
      <el-table-column
        label="创建时间"
        prop="createTime"
        width="180"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="handleEdit(row)"
            v-hasPermi="['business:product-config:update']"
          >
            编辑
          </el-button>
          
          <el-button
            v-if="!row.isDefault"
            type="success"
            link
            @click="handleSetDefault(row)"
            v-hasPermi="['business:product-config:set-default']"
          >
            设为默认
          </el-button>
          
          <el-button
            type="danger"
            link
            @click="handleDelete(row)"
            v-hasPermi="['business:product-config:delete']"
            :disabled="row.isDefault"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- 配置表单弹窗 -->
    <ConfigForm
      ref="configFormRef"
      @success="getList"
    />

    <!-- 价格预览弹窗 -->
    <PricePreview
      ref="pricePreviewRef"
    />
</template>

<script setup lang="ts" name="BusinessConfigIndex">
import { BusinessConfigApi } from '@/api/business/businesscfg/config'
import { BusinessConfigTableItem, BusinessConfigQueryForm } from '@/types/business/businesscfg/config'
import {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getBusinessConfigStatusName,
  getBusinessConfigStatusConfig,
  getBusinessTypeOptions,
  getBusinessConfigStatusOptions
} from '@/utils/business/businesscfg/helpers'
import { PERMISSION_CONFIG } from '@/utils/business/businesscfg/constants'
import { formatDate } from '@/utils/formatTime'
import ConfigForm from './components/ConfigForm.vue'
import PricePreview from './components/PricePreview.vue'

// 响应式数据
const loading = ref(false)
const configList = ref<BusinessConfigTableItem[]>([])
const total = ref(0)
const selectedIds = ref<number[]>([])

// 查询参数
const queryParams = reactive<BusinessConfigQueryForm & { pageNo: number; pageSize: number }>({
  pageNo: 1,
  pageSize: 20,
  businessType: '',
  configName: '',
  status: '',
  isDefault: undefined
})

// 表单引用
const queryFormRef = ref()
const tableRef = ref()
const configFormRef = ref()
const pricePreviewRef = ref()

// 选项数据
const businessTypeOptions = getBusinessTypeOptions()
const statusOptions = getBusinessConfigStatusOptions()

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const data = await BusinessConfigApi.getConfigPage(queryParams)
    console.log('API响应数据:', data)

    configList.value = data.list || []
    total.value = data.total || 0

    console.log('configList.value:', configList.value)
    console.log('total.value:', total.value)

    // 调试：检查第一条记录的详细信息
    if (configList.value.length > 0) {
      console.log('第一条记录详情:', configList.value[0])
      console.log('业务类型:', configList.value[0].businessType)
      console.log('状态:', configList.value[0].status)
    }
  } catch (error) {
    console.error('获取配置列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

// 新增
const handleAdd = () => {
  configFormRef.value?.open('create')
}

// 编辑
const handleEdit = (row: BusinessConfigTableItem) => {
  configFormRef.value?.open('update', row.id)
}

// 删除
const handleDelete = async (row: BusinessConfigTableItem) => {
  if (row.isDefault) {
    ElMessage.warning('不能删除默认配置')
    return
  }
  
  await ElMessageBox.confirm(`确定要删除配置"${row.configName}"吗？`, '提示', {
    type: 'warning'
  })
  
  try {
    await BusinessConfigApi.deleteConfig(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 设为默认
const handleSetDefault = async (row: BusinessConfigTableItem) => {
  await ElMessageBox.confirm(`确定要将"${row.configName}"设为默认配置吗？`, '提示', {
    type: 'warning'
  })
  
  try {
    await BusinessConfigApi.setDefaultConfig(row.id)
    ElMessage.success('设置成功')
    getList()
  } catch (error) {
    console.error('设置失败:', error)
  }
}

// 价格预览
const handlePricePreview = (row: BusinessConfigTableItem) => {
  pricePreviewRef.value?.open(row)
}

// 选择变化
const handleSelectionChange = (selection: BusinessConfigTableItem[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 排序变化
const handleSortChange = ({ prop, order }: any) => {
  // 处理排序逻辑
  getList()
}

// 批量启用
const handleBatchEnable = async () => {
  await ElMessageBox.confirm('确定要启用选中的配置吗？', '提示', {
    type: 'warning'
  })
  
  try {
    await BusinessConfigApi.updateStatus(selectedIds.value, 'ACTIVE')
    ElMessage.success('启用成功')
    getList()
  } catch (error) {
    console.error('启用失败:', error)
  }
}

// 批量禁用
const handleBatchDisable = async () => {
  await ElMessageBox.confirm('确定要禁用选中的配置吗？', '提示', {
    type: 'warning'
  })
  
  try {
    await BusinessConfigApi.updateStatus(selectedIds.value, 'INACTIVE')
    ElMessage.success('禁用成功')
    getList()
  } catch (error) {
    console.error('禁用失败:', error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  // 检查是否包含默认配置
  const hasDefault = configList.value.some(item => 
    selectedIds.value.includes(item.id) && item.isDefault
  )
  
  if (hasDefault) {
    ElMessage.warning('选中的配置中包含默认配置，无法删除')
    return
  }
  
  await ElMessageBox.confirm('确定要删除选中的配置吗？', '提示', {
    type: 'warning'
  })
  
  try {
    await Promise.all(selectedIds.value.map(id => BusinessConfigApi.deleteConfig(id)))
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>


