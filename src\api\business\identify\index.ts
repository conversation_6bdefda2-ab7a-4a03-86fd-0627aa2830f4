import request from '@/config/axios'

// 实名认证 VO
export interface IdentifyVO {
  identifyId: number // 主键
  userId: number // 用户ID
  identifyType: string // 认证类型
  enterpriseType: string // 企业类型
  idType: string // 证件类型
  idName: string // 真实名称
  phone: string // 手机号
  legalName: string // 法人名称
  idNo: string // 证件号码
  faceCard: string // 人像面身份证
  backCard: string // 背面身份证
  license: string // 营业执照
  country: string // 国家
  province: string // 省份
  city: string // 城市
  address: string // 详细地址
  registrationPlace: string // 注册地
  registrationArea: string // 注册区域
  businessScope: string // 主营业务/业务范围
  enterprisePhone: string // 企业电话
  enterpriseEmail: string // 企业邮箱
  businessTerm: string // 营业期限
  statusCd: string // 状态
  statusDate: Date // 状态时间
  createStaff: string // 创建人
  createDate: Date // 创建时间
  remark: string // 备注
  wcUserId: string // 文创链用户ID
}

// 实名认证 API
export const IdentifyApi = {
  // 查询实名认证分页
  getIdentifyPage: async (params: any) => {
    return await request.get({ url: `/business/identify/page`, params })
  },

  // 查询实名认证详情
  getIdentify: async (id: number) => {
    return await request.get({ url: `/business/identify/get?id=` + id })
  },

  // 新增实名认证
  createIdentify: async (data: IdentifyVO) => {
    return await request.post({ url: `/business/identify/create`, data })
  },

  // 修改实名认证
  updateIdentify: async (data: IdentifyVO) => {
    return await request.put({ url: `/business/identify/update`, data })
  },

  // 删除实名认证
  deleteIdentify: async (id: number) => {
    return await request.delete({ url: `/business/identify/delete?id=` + id })
  },

  // 导出实名认证 Excel
  exportIdentify: async (params) => {
    return await request.download({ url: `/business/identify/export-excel`, params })
  },
}
