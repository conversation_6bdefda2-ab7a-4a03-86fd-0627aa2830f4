# 上游同步指南

## 配置多远程仓库

### 1. 添加芋道源码作为上游仓库
```bash
# 添加芋道源码作为上游远程仓库
git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git

# 查看所有远程仓库
git remote -v
# origin    https://github.com/your-repo/wenchuang-chain-manage-html-2.0.git (fetch)
# origin    https://github.com/your-repo/wenchuang-chain-manage-html-2.0.git (push)
# upstream  https://gitee.com/yudaocode/yudao-ui-admin-vue3.git (fetch)
# upstream  https://gitee.com/yudaocode/yudao-ui-admin-vue3.git (push)
```

### 2. 获取上游更新
```bash
# 获取上游仓库的最新代码
git fetch upstream

# 查看上游分支
git branch -r
```

### 3. 创建同步分支
```bash
# 基于当前主分支创建同步分支
git checkout -b sync/upstream-updates

# 合并上游更新（选择性合并）
git merge upstream/master --no-commit --no-ff
```

## 选择性同步策略

### 1. 文件级别同步
```bash
# 只同步特定文件或目录
git checkout upstream/master -- src/components/某个组件
git checkout upstream/master -- src/utils/某个工具函数
git checkout upstream/master -- package.json

# 查看变更
git diff --cached
```

### 2. 功能模块同步
```bash
# 同步特定功能模块
git checkout upstream/master -- src/views/system/
git checkout upstream/master -- src/api/system/

# 排除业务相关文件
git reset HEAD src/views/business/
git reset HEAD src/api/business/
```

### 3. 依赖版本同步
```bash
# 只更新 package.json 中的依赖版本
git show upstream/master:package.json > temp_package.json

# 手动对比和选择性更新依赖
# 然后删除临时文件
rm temp_package.json
```

## 冲突解决策略

### 1. 自动化冲突处理
```bash
# 设置合并策略
git config merge.ours.driver true

# 在 .gitattributes 中配置
echo "src/views/business/* merge=ours" >> .gitattributes
echo "src/api/business/* merge=ours" >> .gitattributes
echo "src/config/business.ts merge=ours" >> .gitattributes
```

### 2. 手动冲突解决
```bash
# 查看冲突文件
git status

# 使用合并工具
git mergetool

# 或手动编辑冲突文件
# 选择保留我们的版本：git checkout --ours 文件名
# 选择保留上游版本：git checkout --theirs 文件名
```

## 定期同步流程

### 1. 每月同步检查
```bash
#!/bin/bash
# scripts/sync-upstream.sh

echo "🔄 开始同步上游更新..."

# 1. 获取上游最新代码
git fetch upstream

# 2. 检查是否有新的提交
UPSTREAM_COMMITS=$(git rev-list HEAD..upstream/master --count)
if [ "$UPSTREAM_COMMITS" -eq 0 ]; then
    echo "✅ 上游无新更新"
    exit 0
fi

echo "📦 发现 $UPSTREAM_COMMITS 个新提交"

# 3. 创建同步分支
SYNC_BRANCH="sync/upstream-$(date +%Y%m%d)"
git checkout -b $SYNC_BRANCH

# 4. 显示上游更新日志
echo "📋 上游更新日志："
git log --oneline HEAD..upstream/master

# 5. 选择性合并
echo "🔀 开始选择性合并..."
git merge upstream/master --no-commit --no-ff

echo "✅ 同步完成，请检查变更并手动处理冲突"
```

### 2. 自动化同步脚本
```bash
#!/bin/bash
# scripts/auto-sync-safe.sh

# 安全的自动同步脚本
SAFE_PATHS=(
    "src/components/common/"
    "src/utils/"
    "src/hooks/"
    "src/directives/"
    "src/styles/common/"
    "types/"
    "build/"
)

for path in "${SAFE_PATHS[@]}"; do
    if git diff --quiet upstream/master HEAD -- "$path"; then
        echo "⏭️  跳过 $path (无变更)"
    else
        echo "🔄 同步 $path"
        git checkout upstream/master -- "$path"
    fi
done
```
