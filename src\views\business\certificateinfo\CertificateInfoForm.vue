<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      v-loading="formLoading"
    >
      <!-- 存证信息分类 -->
      <div class="form-section-title">
        <span class="section-icon">📋</span>
        <span class="section-text">存证信息</span>
      </div>

      <el-form-item label="数据编号" prop="dataNumber">
        <el-input v-model="formData.dataNumber" placeholder="请输入数据编号" disabled />
      </el-form-item>
      <el-form-item label="数据名称" prop="dataName">
        <el-input v-model="formData.dataName" placeholder="请输入数据名称" />
      </el-form-item>
      <el-form-item label="存证单位" prop="certificationUnit">
        <el-input v-model="formData.certificationUnit" placeholder="请输入存证单位" />
      </el-form-item>
      <el-form-item label="数据分类" prop="dataClassification">
        <el-select v-model="formData.dataClassification" placeholder="请选择数据分类">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_CLASSIFICATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据级别" prop="dataLevel">
        <el-select v-model="formData.dataLevel" placeholder="请选择数据级别">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据生成时间" prop="dataGenerationTime">
        <el-date-picker
          v-model="formData.dataGenerationTime"
          type="date"
          value-format="x"
          placeholder="选择数据生成时间"
        />
      </el-form-item>
      <el-form-item label="数据规模" prop="dataScale">
        <div style="display: flex; gap: 10px; width: 100%;">
          <el-input
            v-model="formData.dataScale"
            placeholder="请输入数据规模"
            style="flex: 1; min-width: 0;"
          />
          <el-select
            v-model="formData.dataScaleUnit"
            placeholder="请选择单位"
            style="width: 130px; flex-shrink: 0;"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.DATA_SCALE_UNIT)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="数据资源形态" prop="dataResourceForm">
        <el-select v-model="formData.dataResourceForm" placeholder="请选择数据资源形态">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_RESOURCE_FORM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据资源权属申明" prop="dataResourceOwnership">
        <el-checkbox-group v-model="formData.dataResourceOwnership">
          <el-checkbox
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_RESOURCE_OWNERSHIP)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="数据样本文件路径" prop="dataSampleFile">
        <UploadFile v-model="formData.dataSampleFile" />
      </el-form-item>
      <el-form-item label="限制情况说明" prop="restrictionDescription">
        <el-input
          v-model="formData.restrictionDescription"
          type="textarea"
          :rows="4"
          placeholder="请输入限制情况说明"
        />
      </el-form-item>
      <el-form-item label="其他描述" prop="otherDescription">
        <el-input
          v-model="formData.otherDescription"
          type="textarea"
          :rows="4"
          placeholder="请输入其他描述"
        />
      </el-form-item>
      <el-form-item label="数据块哈希值" prop="dataBlockHashValue">
        <el-input v-model="formData.dataBlockHashValue" placeholder="请输入数据块哈希值" />
      </el-form-item>
      <el-form-item label="数据访问地址" prop="dataAccessAddress">
        <el-input
          v-model="formData.dataAccessAddress"
          type="textarea"
          :rows="3"
          placeholder="请输入数据访问地址"
        />
      </el-form-item>
      <el-form-item label="扩展信息哈希值" prop="extendedInfoHashValue">
        <el-input v-model="formData.extendedInfoHashValue" placeholder="请输入扩展信息哈希值" />
      </el-form-item>
      <el-form-item label="扩展信息访问地址" prop="extendedInfoAccessAddress">
        <el-input
          v-model="formData.extendedInfoAccessAddress"
          type="textarea"
          :rows="3"
          placeholder="请输入扩展信息访问地址"
        />
      </el-form-item>

      <!-- 扩展信息分类 -->
      <div class="form-section-title">
        <span class="section-icon">🔗</span>
        <span class="section-text">扩展信息</span>
      </div>

      <el-form-item label="数据来源信息" prop="dataSourceInfo">
        <div style="display: flex; gap: 10px; width: 100%;">
          <el-select
            v-model="formData.dataSourceInfo"
            placeholder="请选择数据来源信息"
            style="width: 120px; flex-shrink: 0;"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.DATA_SOURCE_INFO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <el-input
            v-model="formData.dataSourceSpecificInfo"
            placeholder="请输入数据来源具体信息"
            style="flex: 1; min-width: 0;"
          />
        </div>
      </el-form-item>
      <el-form-item label="数据来源佐证材料路径" prop="dataSourceEvidenceMaterial">
        <UploadFile v-model="formData.dataSourceEvidenceMaterial" />
      </el-form-item>
      <el-form-item label="有效控制措施佐证材料路径" prop="effectiveControlEvidenceMaterial">
        <UploadFile v-model="formData.effectiveControlEvidenceMaterial" />
      </el-form-item>
      <el-form-item label="个人信息采集的合法佐证材料路径" prop="personalInfoCollectionEvidenceMaterial">
        <UploadFile v-model="formData.personalInfoCollectionEvidenceMaterial" />
      </el-form-item>

      <!-- 其他信息分类 -->
      <div class="form-section-title">
        <span class="section-icon">⚙️</span>
        <span class="section-text">其他信息</span>
      </div>

      <!-- <el-form-item label="审核状态" prop="statusCd">
        <el-select v-model="formData.statusCd" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { CertificateInfoApi, CertificateInfoVO } from '@/api/business/certificateinfo'

/** 存证信息表，用于记录各类数据存证相关信息 表单 */
defineOptions({ name: 'CertificateInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  dataNumber: undefined,
  dataName: undefined,
  certificationUnit: undefined,
  dataClassification: undefined,
  dataLevel: undefined,
  dataGenerationTime: undefined,
  dataScale: undefined,
  dataScaleUnit: undefined,
  dataResourceForm: undefined,
  dataResourceOwnership: [],
  dataSampleFile: undefined,
  restrictionDescription: undefined,
  otherDescription: undefined,
  dataBlockHashValue: undefined,
  dataAccessAddress: undefined,
  extendedInfoHashValue: undefined,
  extendedInfoAccessAddress: undefined,
  dataSourceInfo: undefined,
  dataSourceSpecificInfo: undefined,
  dataSourceEvidenceMaterial: undefined,
  effectiveControlEvidenceMaterial: undefined,
  personalInfoCollectionEvidenceMaterial: undefined,
  remark: undefined
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await CertificateInfoApi.getCertificateInfo(id)
      formData.value = data
      // 处理dataResourceOwnership字段的反序列化
      if (data.dataResourceOwnership && typeof data.dataResourceOwnership === 'string') {
        try {
          formData.value.dataResourceOwnership = JSON.parse(data.dataResourceOwnership)
        } catch (e) {
          formData.value.dataResourceOwnership = []
        }
      } else if (!data.dataResourceOwnership) {
        formData.value.dataResourceOwnership = []
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as CertificateInfoVO
    // 处理dataResourceOwnership字段的序列化
    if (Array.isArray(data.dataResourceOwnership)) {
      data.dataResourceOwnership = JSON.stringify(data.dataResourceOwnership) as any
    }

    if (formType.value === 'create') {
      await CertificateInfoApi.createCertificateInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await CertificateInfoApi.updateCertificateInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    dataNumber: undefined,
    dataName: undefined,
    certificationUnit: undefined,
    dataClassification: undefined,
    dataLevel: undefined,
    dataGenerationTime: undefined,
    dataScale: undefined,
    dataScaleUnit: undefined,
    dataResourceForm: undefined,
    dataResourceOwnership: [],
    dataSampleFile: undefined,
    restrictionDescription: undefined,
    otherDescription: undefined,
    dataBlockHashValue: undefined,
    dataAccessAddress: undefined,
    extendedInfoHashValue: undefined,
    extendedInfoAccessAddress: undefined,
    dataSourceInfo: undefined,
    dataSourceSpecificInfo: undefined,
    dataSourceEvidenceMaterial: undefined,
    effectiveControlEvidenceMaterial: undefined,
    personalInfoCollectionEvidenceMaterial: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-section-title {
  display: flex;
  align-items: center;
  margin: 20px 0 15px 0;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-left: 4px solid #409EFF;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
}

.section-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  letter-spacing: 0.5px;
}
</style>
