#!/usr/bin/env node

/**
 * 调试配置测试脚本
 * 验证调试环境是否正确配置
 */

const fs = require('fs');
const path = require('path');
const { checkServer } = require('./wait-for-server');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    log('green', `✅ ${description}: ${filePath}`);
    return true;
  } else {
    log('red', `❌ ${description}: ${filePath} (文件不存在)`);
    return false;
  }
}

function checkConfig(filePath, key, expectedValue, description) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    const content = fs.readFileSync(fullPath, 'utf8');
    
    if (filePath.endsWith('.json')) {
      const config = JSON.parse(content);
      const value = key.split('.').reduce((obj, k) => obj && obj[k], config);
      if (value === expectedValue) {
        log('green', `✅ ${description}: ${key} = ${value}`);
        return true;
      } else {
        log('yellow', `⚠️  ${description}: ${key} = ${value} (期望: ${expectedValue})`);
        return false;
      }
    } else {
      // 环境文件
      const regex = new RegExp(`^${key}\\s*=\\s*(.+)$`, 'm');
      const match = content.match(regex);
      if (match) {
        const value = match[1].trim().replace(/['"]/g, '');
        if (value === expectedValue) {
          log('green', `✅ ${description}: ${key} = ${value}`);
          return true;
        } else {
          log('yellow', `⚠️  ${description}: ${key} = ${value} (期望: ${expectedValue})`);
          return false;
        }
      } else {
        log('red', `❌ ${description}: 未找到配置项 ${key}`);
        return false;
      }
    }
  } catch (error) {
    log('red', `❌ ${description}: 读取配置失败 - ${error.message}`);
    return false;
  }
}

async function testDebugConfig() {
  log('blue', '🔍 开始检查调试配置...\n');

  let allPassed = true;

  // 检查必要文件
  log('blue', '📁 检查配置文件:');
  allPassed &= checkFile('.vscode/launch.json', 'VSCode 调试配置');
  allPassed &= checkFile('.vscode/tasks.json', 'VSCode 任务配置');
  allPassed &= checkFile('.vscode/settings.json', 'VSCode 设置');
  allPassed &= checkFile('.env.local', '本地环境配置');
  allPassed &= checkFile('.env.dev', '开发环境配置');
  allPassed &= checkFile('vite.config.ts', 'Vite 配置');
  console.log();

  // 检查环境配置
  log('blue', '⚙️  检查环境配置:');
  allPassed &= checkConfig('.env.local', 'VITE_PORT', '3000', '本地开发端口');
  allPassed &= checkConfig('.env.local', 'VITE_SOURCEMAP', 'true', '本地 Sourcemap');
  allPassed &= checkConfig('.env.dev', 'VITE_PORT', '3000', '开发环境端口');
  allPassed &= checkConfig('.env.dev', 'VITE_SOURCEMAP', 'true', '开发环境 Sourcemap');
  console.log();

  // 检查 VSCode 配置
  log('blue', '🔧 检查 VSCode 调试配置:');
  try {
    const launchConfig = JSON.parse(fs.readFileSync('.vscode/launch.json', 'utf8'));
    const configs = launchConfig.configurations || [];
    
    const requiredConfigs = [
      '调试：本地开发环境 (3000端口)',
      '调试：本地开发环境 - Chrome (3000端口)',
      '快速调试：连接到3000端口'
    ];
    
    requiredConfigs.forEach(name => {
      const config = configs.find(c => c.name === name);
      if (config) {
        log('green', `✅ 调试配置: ${name}`);
        if (config.url && config.url.includes('3000')) {
          log('green', `  └─ URL: ${config.url}`);
        }
      } else {
        log('red', `❌ 调试配置: ${name} (未找到)`);
        allPassed = false;
      }
    });
  } catch (error) {
    log('red', `❌ 读取调试配置失败: ${error.message}`);
    allPassed = false;
  }
  console.log();

  // 检查辅助脚本
  log('blue', '📜 检查辅助脚本:');
  allPassed &= checkFile('scripts/debug-helper.js', '调试助手脚本');
  allPassed &= checkFile('scripts/wait-for-server.js', '服务器等待脚本');
  allPassed &= checkFile('docs/DEBUG_GUIDE.md', '调试指南文档');
  console.log();

  // 总结
  if (allPassed) {
    log('green', '🎉 所有调试配置检查通过！');
    log('blue', '\n📋 使用说明:');
    console.log('1. 启动开发服务器: pnpm dev');
    console.log('2. 在 VSCode 中按 F5 开始调试');
    console.log('3. 选择"快速调试：连接到3000端口"');
    console.log('4. 如有问题，参考 docs/DEBUG_GUIDE.md');
  } else {
    log('yellow', '⚠️  部分配置可能需要调整，但基本功能应该可用');
    log('blue', '\n🔧 建议操作:');
    console.log('1. 检查上述标记为 ❌ 的项目');
    console.log('2. 参考 docs/DEBUG_GUIDE.md 进行故障排除');
    console.log('3. 如需帮助，联系开发团队');
  }
}

// 主程序
if (require.main === module) {
  testDebugConfig().catch(error => {
    log('red', `❌ 测试失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testDebugConfig };
