/**
 * lint-staged 配置文件
 *
 * 用于在 git commit 前对暂存区的文件进行代码检查和格式化
 * 只对变更的文件进行处理，提高效率
 */

module.exports = {
  // JavaScript/TypeScript/Vue 文件
  '*.{js,jsx,ts,tsx,vue}': [
    // ESLint 检查和自动修复
    'eslint --fix',
    // Prettier 格式化
    'prettier --write'
  ],

  // 样式文件
  '*.{css,scss,less,vue}': [
    // Stylelint 检查和自动修复
    'stylelint --fix',
    // Prettier 格式化
    'prettier --write'
  ],

  // JSON、Markdown、HTML 文件
  '*.{json,md,html}': [
    // Prettier 格式化
    'prettier --write'
  ],

  // YAML 文件
  '*.{yml,yaml}': [
    // Prettier 格式化
    'prettier --write'
  ],

  // 包管理文件
  'package.json': [
    // Prettier 格式化
    'prettier --write'
  ]
}
