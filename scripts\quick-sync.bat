@echo off
setlocal enabledelayedexpansion

REM 文创链服务平台 2.0 - 上游快速同步脚本 (Windows版)
REM 用于安全地同步芋道源码的基础组件和工具函数更新
REM 
REM 使用方法:
REM   scripts\quick-sync.bat

echo.
echo 🚀 文创链服务平台 2.0 - 上游快速同步工具
echo ==================================================
echo.

REM 检查是否在 Git 仓库中
git rev-parse --git-dir >nul 2>&1
if errorlevel 1 (
    echo ❌ 当前目录不是 Git 仓库
    pause
    exit /b 1
)

REM 检查是否有未提交的更改
git diff-index --quiet HEAD -- >nul 2>&1
if errorlevel 1 (
    echo ⚠️  工作目录有未提交的更改
    echo 请先提交或暂存您的更改：
    git status --porcelain
    set /p continue="是否继续？(y/N): "
    if /i not "!continue!"=="y" (
        echo ℹ️  同步已取消
        pause
        exit /b 0
    )
)

REM 检查上游仓库配置
git remote get-url upstream >nul 2>&1
if errorlevel 1 (
    echo 🔄 配置上游仓库...
    git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git
    if errorlevel 1 (
        echo ❌ 配置上游仓库失败
        pause
        exit /b 1
    )
    echo ✅ 上游仓库配置完成
) else (
    echo ℹ️  上游仓库已配置
)

REM 获取上游更新
echo 🔄 获取上游更新...
git fetch upstream
if errorlevel 1 (
    echo ❌ 获取上游更新失败
    pause
    exit /b 1
)
echo ✅ 上游更新获取完成

REM 检查上游更新数量
for /f %%i in ('git rev-list HEAD..upstream/master --count 2^>nul') do set updates=%%i
if "!updates!"=="" set updates=0

if !updates! equ 0 (
    echo ✅ 无新更新，当前已是最新版本
    pause
    exit /b 0
)

echo ℹ️  发现 !updates! 个新提交

REM 显示上游更新日志
echo ℹ️  📋 最近的上游更新日志：
echo ----------------------------------------
git log --oneline HEAD..upstream/master | head -10
echo ----------------------------------------
echo.

REM 确认同步
set /p continue="是否继续执行同步？(y/N): "
if /i not "!continue!"=="y" (
    echo ℹ️  同步已取消
    pause
    exit /b 0
)

REM 创建同步分支
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set mydate=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set mytime=!mytime: =0!
set sync_branch=sync/quick-!mydate!-!mytime!

echo 🔄 创建同步分支: !sync_branch!
git checkout -b !sync_branch!
if errorlevel 1 (
    echo ❌ 创建同步分支失败
    pause
    exit /b 1
)
echo ✅ 同步分支创建成功

REM 同步安全模块
echo 🔄 开始同步安全模块...

set synced_count=0
set skipped_count=0

REM 定义安全同步的模块
set safe_modules=src/components/Form/ src/components/Table/ src/components/Dialog/ src/components/Upload/ src/components/Icon/ src/components/Editor/ src/utils/auth.ts src/utils/dict.ts src/utils/download.ts src/utils/validate.ts src/utils/tree.ts types/

REM 定义需要谨慎同步的模块
set careful_modules=src/views/ai/ src/api/ai/ src/views/iot/ src/api/iot/

echo 🔄 同步安全模块...
for %%m in (!safe_modules!) do (
    echo ℹ️  检查模块: %%m
    
    REM 检查模块是否存在于上游
    git cat-file -e upstream/master:%%m >nul 2>&1
    if not errorlevel 1 (
        REM 检查是否有变更
        git diff --quiet HEAD upstream/master -- %%m >nul 2>&1
        if not errorlevel 1 (
            echo   ⏭️  无变更，跳过
            set /a skipped_count+=1
        ) else (
            REM 有变更，进行同步
            git checkout upstream/master -- %%m >nul 2>&1
            if not errorlevel 1 (
                echo   ✅ 同步成功
                set /a synced_count+=1
            ) else (
                echo   ⚠️  同步失败
            )
        )
    ) else (
        echo   ⚠️  上游不存在
        set /a skipped_count+=1
    )
)

echo ✅ 安全模块同步完成: !synced_count! 个已同步, !skipped_count! 个跳过

REM 询问是否同步谨慎模块
echo.
echo ℹ️  发现 AI 和 IoT 模块，这些模块来自原框架但可能有定制
set /p sync_careful="是否检查并选择性同步这些模块？(y/N): "
if /i "!sync_careful!"=="y" (
    echo 🔄 检查需要谨慎同步的模块...
    for %%m in (!careful_modules!) do (
        echo ℹ️  检查模块: %%m

        REM 检查模块是否存在于上游
        git cat-file -e upstream/master:%%m >nul 2>&1
        if not errorlevel 1 (
            REM 检查是否有变更
            git diff --quiet HEAD upstream/master -- %%m >nul 2>&1
            if not errorlevel 1 (
                echo   ⏭️  无变更，跳过
            ) else (
                echo   ⚠️  发现变更
                set /p sync_module="    是否同步 %%m？(y/N): "
                if /i "!sync_module!"=="y" (
                    git checkout upstream/master -- %%m >nul 2>&1
                    if not errorlevel 1 (
                        echo     ✅ 同步成功
                        set /a synced_count+=1
                    ) else (
                        echo     ❌ 同步失败
                    )
                ) else (
                    echo     ⏭️  用户跳过
                )
            )
        ) else (
            echo   ⚠️  上游不存在
        )
    )
    echo ✅ 谨慎模块处理完成
)

REM 检查同步结果
echo 🔄 检查同步结果...
git diff --quiet --cached >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  没有检测到任何变更
    echo 🔄 切换回主分支并删除同步分支...
    git checkout main >nul 2>&1
    git branch -D !sync_branch! >nul 2>&1
    pause
    exit /b 0
)

echo ℹ️  检测到以下变更：
git status --porcelain

REM 运行基本验证
echo 🔄 运行基本验证...

REM 检查 package.json 语法
where node >nul 2>&1
if not errorlevel 1 (
    node -e "JSON.parse(require('fs').readFileSync('package.json', 'utf8'))" >nul 2>&1
    if not errorlevel 1 (
        echo ✅ package.json 语法正确
    ) else (
        echo ❌ package.json 语法错误
        pause
        exit /b 1
    )
)

REM 检查 TypeScript 配置
if exist tsconfig.json (
    where node >nul 2>&1
    if not errorlevel 1 (
        node -e "JSON.parse(require('fs').readFileSync('tsconfig.json', 'utf8'))" >nul 2>&1
        if not errorlevel 1 (
            echo ✅ tsconfig.json 语法正确
        ) else (
            echo ❌ tsconfig.json 语法错误
            pause
            exit /b 1
        )
    )
)

echo ✅ 基本验证通过

REM 提交同步结果
echo 🔄 提交同步结果...
git add .

REM 生成提交信息
set commit_message=🔄 上游同步: 安全同步基础组件和工具函数

git commit -m "!commit_message!" --no-verify
if errorlevel 1 (
    echo ❌ 提交失败
    pause
    exit /b 1
)
echo ✅ 同步结果提交成功

REM 显示后续操作建议
echo.
echo ✅ 🎉 上游同步完成！
echo.
echo ℹ️  📋 后续操作建议：
echo 1. 测试功能是否正常：
echo    pnpm run dev
echo    pnpm run lint:eslint
echo    pnpm run ts:check
echo.
echo 2. 如果测试通过，合并到主分支：
echo    git checkout main
echo    git merge !sync_branch!
echo    git branch -d !sync_branch!
echo.
echo 3. 如果有问题，可以回滚：
echo    git checkout main
echo    git branch -D !sync_branch!
echo.
echo ⚠️  请务必测试后再合并到主分支！

pause
