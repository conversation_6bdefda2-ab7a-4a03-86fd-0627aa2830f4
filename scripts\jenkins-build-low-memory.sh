#!/bin/bash

# Jenkins低内存构建脚本
# 专门为内存受限环境设计的构建脚本

set -e

echo "🚀 开始低内存模式构建..."

# 设置内存限制环境变量 - 极限内存优化
export NODE_OPTIONS="--max_old_space_size=1024 --max-semi-space-size=32"
export VITE_BUILD_CHUNK_SIZE_LIMIT=500
export VITE_DROP_CONSOLE=true
export VITE_DROP_DEBUGGER=true
export VITE_SOURCEMAP=false
export VITE_OUT_DIR=dist-prod
export VITE_BUILD_MINIFY=false
export VITE_BUILD_INCREMENTAL=true

echo "📊 当前内存状态:"
free -h

echo "🧹 清理缓存和临时文件..."
rm -rf node_modules/.cache || true
rm -rf .vite || true
rm -rf .swc || true
rm -rf dist-prod || true
rm -rf core.* || true

# 强制垃圾回收
sync
echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true

echo "📊 清理后内存状态:"
free -h

echo "🔧 设置Git配置以减少内存使用..."
git config --local core.preloadindex false
git config --local core.fscache false
git config --local gc.auto 0

echo "🔨 开始构建..."
# 使用极限内存限制，禁用压缩以减少内存使用
echo "⚠️  使用极限内存模式，禁用代码压缩以减少内存消耗"
node --max_old_space_size=1024 \
     --max-semi-space-size=32 \
     --expose-gc \
     ./node_modules/vite/bin/vite.js build --mode prod --minify=false

echo "✅ 构建完成!"

echo "📊 构建后内存状态:"
free -h

echo "📁 构建产物信息:"
if [ -d "dist-prod" ]; then
    echo "构建成功！"
    echo "构建产物大小: $(du -sh dist-prod)"
    echo "文件数量: $(find dist-prod -type f | wc -l)"
    
    # 列出最大的几个文件
    echo "最大的构建文件:"
    find dist-prod -type f -exec ls -lh {} \; | sort -k5 -hr | head -10
else
    echo "构建失败！dist-prod目录不存在"
    exit 1
fi

echo "🎉 低内存构建流程完成!"
