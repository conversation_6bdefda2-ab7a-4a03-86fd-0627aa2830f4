/**
 * 上游同步配置文件
 * 定义哪些文件/目录可以安全同步，哪些需要保护
 */

module.exports = {
  // 上游仓库配置
  upstream: {
    remote: 'upstream',
    url: 'https://gitee.com/yudaocode/yudao-ui-admin-vue3.git',
    branch: 'master'
  },

  // 同步策略配置
  syncStrategy: {
    // 自动同步 - 这些文件/目录可以安全地自动同步
    autoSync: [
      // 基础组件
      'src/components/Form/',
      'src/components/Table/',
      'src/components/Dialog/',
      'src/components/Upload/',
      'src/components/Icon/',
      'src/components/Editor/',
      'src/components/Descriptions/',
      'src/components/Search/',
      'src/components/Sticky/',
      'src/components/Highlight/',
      
      // 工具函数
      'src/utils/auth.ts',
      'src/utils/request.ts',
      'src/utils/validate.ts',
      'src/utils/tree.ts',
      'src/utils/dict.ts',
      'src/utils/download.ts',
      'src/utils/formatTime.ts',
      'src/utils/index.ts',
      
      // Hooks
      'src/hooks/web/useAuth.ts',
      'src/hooks/web/useDict.ts',
      'src/hooks/web/useMessage.ts',
      'src/hooks/web/useTable.ts',
      'src/hooks/web/useCrudSchemas.ts',
      
      // 指令
      'src/directives/',
      
      // 类型定义
      'types/components.d.ts',
      'types/env.d.ts',
      'types/global.d.ts',
      'types/router.d.ts',
      
      // 样式文件（通用部分）
      'src/styles/variables.scss',
      'src/styles/index.scss',
      'src/styles/element.scss',
    ],

    // 手动检查 - 这些需要人工审查后决定是否同步
    manualReview: [
      // 系统管理（可能有定制）
      'src/views/system/',
      'src/api/system/',

      // 基础设施
      'src/views/infra/',
      'src/api/infra/',

      // 工作流
      'src/views/bpm/',
      'src/api/bpm/',

      // AI 模块（原框架提供，但可能有定制）
      'src/views/ai/',
      'src/api/ai/',

      // IoT 模块（原框架提供，但可能有定制）
      'src/views/iot/',
      'src/api/iot/',

      // 构建配置
      'build/',
      'vite.config.ts',
      'tsconfig.json',
      'uno.config.ts',
      'package.json',

      // 路由配置
      'src/router/',

      // 布局组件
      'src/layout/',

      // 权限控制
      'src/permission.ts',

      // 主入口
      'src/main.ts',
      'src/App.vue',
    ],

    // 永不同步 - 业务相关文件，绝对不能被覆盖
    neverSync: [
      // 文创链业务模块（项目特有）
      'src/views/business/',
      'src/api/business/',

      // 其他业务相关
      'src/config/business.ts',
      'src/assets/business/',

      // 环境配置
      '.env*',

      // 项目特定配置
      'README.md',
      'LICENSE',
      'docs/',

      // Git 配置
      '.gitignore',
      '.gitattributes',

      // 部署相关
      'docker/',
      'nginx.conf',
      'Dockerfile',
    ]
  },

  // 依赖同步配置
  dependencySync: {
    // 自动更新的依赖类型
    autoUpdate: [
      '@types/*',           // TypeScript 类型定义
      'eslint*',           // ESLint 相关
      'prettier*',         // Prettier 相关
      'stylelint*',        // Stylelint 相关
      '@commitlint/*',     // Commitlint 相关
      'husky',             // Git hooks
      'lint-staged',       // 代码检查
    ],
    
    // 需要谨慎更新的核心依赖
    manualUpdate: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      'vite',
      'typescript',
      '@vitejs/*',
      '@vue/*',
    ],
    
    // 不更新的依赖（业务特定）
    skipUpdate: [
      // 文创链特定依赖
      'business-specific-lib',
      // 其他不希望更新的依赖
    ]
  },

  // 冲突解决策略
  conflictResolution: {
    // 默认策略：保留我们的版本
    default: 'ours',
    
    // 特定文件的策略
    fileStrategies: {
      'package.json': 'manual',           // 手动处理
      'src/main.ts': 'ours',             // 保留我们的
      'src/App.vue': 'ours',             // 保留我们的
      'src/permission.ts': 'ours',       // 保留我们的
      'vite.config.ts': 'manual',        // 手动处理
    }
  },

  // 同步后的验证步骤
  postSyncValidation: [
    'npm run ts:check',      // TypeScript 检查
    'npm run lint:eslint',   // ESLint 检查
    'npm run lint:style',    // 样式检查
    'npm run build:test',    // 测试构建
  ],

  // 通知配置
  notifications: {
    // 同步完成后的通知
    onComplete: {
      console: true,
      // email: '<EMAIL>',  // 可选：邮件通知
      // webhook: 'https://hooks.slack.com/...',  // 可选：Slack 通知
    },
    
    // 冲突发生时的通知
    onConflict: {
      console: true,
      // 其他通知方式...
    }
  },

  // 备份配置
  backup: {
    enabled: true,
    path: '.sync-backups/',
    keepDays: 30,  // 保留30天的备份
  }
}
