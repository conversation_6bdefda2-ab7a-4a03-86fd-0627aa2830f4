# 上游同步实施指南

## 🎯 方案选择建议

根据您的项目情况，推荐采用 **组合方案**：

### 📋 推荐实施步骤

#### 第一阶段：基础配置（立即实施）
```bash
# 1. 配置上游仓库
git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git
git fetch upstream

# 2. 创建同步配置
cp sync.config.js ./
cp scripts/sync-modules.js ./scripts/

# 3. 设置执行权限
chmod +x scripts/sync-modules.js
```

#### 第二阶段：手动同步测试（本周内）
```bash
# 1. 执行首次同步检查
node scripts/sync-modules.js

# 2. 检查同步结果
git status
git diff

# 3. 测试功能
pnpm run dev
pnpm run build:test
```

#### 第三阶段：自动化配置（下周内）
```bash
# 1. 配置 GitHub Actions
cp .github/workflows/upstream-sync.yml ./.github/workflows/

# 2. 设置定期检查
# 每周一自动检查上游更新

# 3. 配置通知（可选）
# Slack、邮件等通知方式
```

## 🔧 详细操作指南

### 1. 立即可用的手动同步

```bash
#!/bin/bash
# 快速同步脚本

echo "🚀 开始快速同步..."

# 获取上游更新
git fetch upstream

# 检查更新数量
UPDATES=$(git rev-list HEAD..upstream/master --count)
if [ "$UPDATES" -eq 0 ]; then
    echo "✅ 无新更新"
    exit 0
fi

echo "📦 发现 $UPDATES 个更新"

# 创建同步分支
BRANCH="sync/quick-$(date +%m%d)"
git checkout -b $BRANCH

# 安全同步基础组件
echo "🔄 同步基础组件..."
git checkout upstream/master -- src/components/Form/ 2>/dev/null || echo "跳过 Form"
git checkout upstream/master -- src/components/Table/ 2>/dev/null || echo "跳过 Table"
git checkout upstream/master -- src/components/Dialog/ 2>/dev/null || echo "跳过 Dialog"

# 同步工具函数
echo "🔄 同步工具函数..."
git checkout upstream/master -- src/utils/auth.ts 2>/dev/null || echo "跳过 auth.ts"
git checkout upstream/master -- src/utils/request.ts 2>/dev/null || echo "跳过 request.ts"
git checkout upstream/master -- src/utils/validate.ts 2>/dev/null || echo "跳过 validate.ts"

# 同步类型定义
echo "🔄 同步类型定义..."
git checkout upstream/master -- types/ 2>/dev/null || echo "跳过 types"

echo "✅ 快速同步完成"
echo "💡 请检查变更: git status"
echo "💡 测试完成后合并: git checkout main && git merge $BRANCH"
```

### 2. 依赖版本同步策略

```bash
#!/bin/bash
# 依赖版本检查脚本

echo "📦 检查依赖版本差异..."

# 获取上游 package.json
git show upstream/master:package.json > /tmp/upstream-package.json

# 比较版本差异
echo "🔍 核心依赖版本对比:"
echo "| 依赖 | 当前版本 | 上游版本 | 建议 |"
echo "|------|----------|----------|------|"

# Vue 生态
CURRENT_VUE=$(jq -r '.dependencies.vue' package.json)
UPSTREAM_VUE=$(jq -r '.dependencies.vue' /tmp/upstream-package.json)
echo "| Vue | $CURRENT_VUE | $UPSTREAM_VUE | 保持当前 |"

CURRENT_EP=$(jq -r '.dependencies["element-plus"]' package.json)
UPSTREAM_EP=$(jq -r '.dependencies["element-plus"]' /tmp/upstream-package.json)
echo "| Element Plus | $CURRENT_EP | $UPSTREAM_EP | 保持当前 |"

# 开发工具
CURRENT_VITE=$(jq -r '.devDependencies.vite' package.json)
UPSTREAM_VITE=$(jq -r '.devDependencies.vite' /tmp/upstream-package.json)
echo "| Vite | $CURRENT_VITE | $UPSTREAM_VITE | 保持当前 |"

echo ""
echo "💡 建议: 您的版本更新，无需降级"

# 清理临时文件
rm /tmp/upstream-package.json
```

### 3. 冲突预防配置

创建 `.gitattributes` 文件：
```bash
# 业务模块永远使用我们的版本
src/views/business/* merge=ours
src/api/business/* merge=ours
src/views/ai/* merge=ours
src/api/ai/* merge=ours
src/views/iot/* merge=ours
src/api/iot/* merge=ours

# 配置文件需要手动处理
package.json merge=manual
vite.config.ts merge=manual
src/main.ts merge=ours
src/App.vue merge=ours
src/permission.ts merge=ours

# 文档和配置保持我们的版本
README.md merge=ours
docs/* merge=ours
.env* merge=ours
```

## 📋 同步检查清单

### 同步前检查
- [ ] 当前分支代码已提交
- [ ] 备份重要的业务文件
- [ ] 确认上游仓库配置正确
- [ ] 检查是否有未解决的冲突

### 同步后验证
- [ ] TypeScript 编译通过
- [ ] ESLint 检查通过
- [ ] 样式检查通过
- [ ] 构建测试通过
- [ ] 核心功能测试正常
- [ ] 业务模块未受影响

### 合并前确认
- [ ] 代码审查完成
- [ ] 功能测试通过
- [ ] 性能测试正常
- [ ] 文档更新完成

## 🚨 风险控制

### 高风险操作
```bash
# ❌ 绝对不要执行的操作
git merge upstream/master  # 可能覆盖所有业务代码
git reset --hard upstream/master  # 会丢失所有本地修改
git checkout upstream/master -- src/  # 会覆盖所有源码
```

### 安全操作
```bash
# ✅ 推荐的安全操作
git checkout upstream/master -- src/components/Form/  # 只同步特定组件
git merge upstream/master --no-commit --no-ff  # 合并但不提交，可以检查
git checkout -b sync-test  # 在新分支测试
```

## 📞 问题处理

### 常见问题

1. **合并冲突**
   ```bash
   # 查看冲突文件
   git status
   
   # 选择保留我们的版本
   git checkout --ours 冲突文件
   
   # 选择上游版本
   git checkout --theirs 冲突文件
   ```

2. **依赖冲突**
   ```bash
   # 重新安装依赖
   rm -rf node_modules package-lock.json
   pnpm install
   ```

3. **构建失败**
   ```bash
   # 检查 TypeScript 错误
   pnpm run ts:check
   
   # 检查代码规范
   pnpm run lint:eslint
   ```

### 紧急回滚
```bash
# 如果同步出现严重问题，立即回滚
git checkout main
git branch -D sync-branch-name
git reset --hard HEAD~1  # 如果已经合并
```

## 📈 持续优化

### 定期评估（每月）
- 评估同步效果
- 优化同步配置
- 更新风险控制策略
- 完善自动化流程

### 团队协作
- 建立同步责任人制度
- 定期培训团队成员
- 建立问题反馈机制
- 完善文档和流程
