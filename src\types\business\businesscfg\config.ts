// 业务类型枚举
export enum BusinessTypeEnum {
  DATA_CERTIFICATE_EVIDENCE = 'data_certificate_evidence',
  SOFTWARE_REGISTER = 'software_register', 
  REAL_IDENTIFY = 'real_identify',
  COPYRIGHT_REGISTER = 'copyright_register',
  BLOCKCHAIN_EVIDENCE = 'blockchain_evidence'
}

// 业务类型显示名称映射
export const BusinessTypeNames: Record<BusinessTypeEnum, string> = {
  [BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE]: '数据存证',
  [BusinessTypeEnum.SOFTWARE_REGISTER]: '软件著作权登记',
  [BusinessTypeEnum.REAL_IDENTIFY]: '实名认证',
  [BusinessTypeEnum.COPYRIGHT_REGISTER]: '版权登记',
  [BusinessTypeEnum.BLOCKCHAIN_EVIDENCE]: '区块链存证'
}

// 业务配置状态枚举
export enum BusinessConfigStatusEnum {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

// 业务配置状态显示名称映射
export const BusinessConfigStatusNames: Record<BusinessConfigStatusEnum, string> = {
  [BusinessConfigStatusEnum.ACTIVE]: '启用',
  [BusinessConfigStatusEnum.INACTIVE]: '禁用'
}

// 业务配置表单数据
export interface BusinessConfigFormData {
  id?: number
  businessType: string
  productSpuId: number
  configName: string
  serviceDescription?: string
  processingDays: number
  isDefault: boolean
  status: string
  sortOrder: number
}

// 业务配置表格项
export interface BusinessConfigTableItem {
  id: number
  businessType: string
  businessTypeName: string
  productSpuId: number
  configName: string
  serviceDescription?: string
  processingDays: number
  isDefault: boolean
  status: string
  statusName: string
  sortOrder: number
  createTime: string
  updateTime: string
  // 扩展字段
  productInfo?: {
    spuId: number
    name: string
    price: number
    memberPrices?: {
      bronze: number
      silver: number
      gold: number
    }
  }
}

// 业务配置查询表单
export interface BusinessConfigQueryForm {
  businessType?: string
  configName?: string
  status?: string
  isDefault?: boolean
}

// 价格预览数据
export interface PricePreviewData {
  originalPrice: number
  memberPrices: {
    bronze: number
    silver: number
    gold: number
  }
  discountRates: {
    bronze: number
    silver: number
    gold: number
  }
}

// 服务对比数据
export interface ServiceComparisonData {
  configId: number
  configName: string
  originalPrice: number
  memberPrices: {
    bronze: number
    silver: number
    gold: number
  }
  processingDays: number
  features: string[]
  isDefault: boolean
  isRecommended: boolean
}

// 业务类型选项
export interface BusinessTypeOption {
  value: string
  label: string
  description?: string
  icon?: string
}

// 默认配置管理数据
export interface DefaultConfigData {
  businessType: string
  currentDefaultId?: number
  configs: BusinessConfigTableItem[]
}

export default {
  BusinessTypeEnum,
  BusinessTypeNames,
  BusinessConfigStatusEnum,
  BusinessConfigStatusNames
}
