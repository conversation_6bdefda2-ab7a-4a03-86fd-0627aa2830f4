import request from '@/config/axios'

// 业务订单关联分页请求
export interface BusinessOrderRelationPageReq {
  pageNo: number
  pageSize: number
  orderId?: number
  businessType?: string
  businessStatus?: string
  businessProductConfigId?: number
  createTime?: string[]
}

// 业务订单关联更新请求
export interface BusinessOrderRelationUpdateReq {
  id: number
  businessStatus: string
  processResult?: string
  businessData?: string
}

// 业务订单关联响应
export interface BusinessOrderRelationRespVO {
  id: number
  orderId: number
  businessType: string
  businessId?: number
  businessProductConfigId: number
  businessStatus: string
  businessData?: string
  processResult?: string
  createTime: string
  updateTime: string
  // 关联订单信息
  orderInfo?: {
    orderNo: string
    userId: number
    userName?: string
    totalAmount: number
    payStatus: string
    payTime?: string
  }
  // 关联配置信息
  configInfo?: {
    configName: string
    processingDays: number
    serviceDescription?: string
  }
}

// 业务状态统计
export interface BusinessStatusStatistics {
  businessType: string
  businessTypeName: string
  statusCounts: {
    [status: string]: number
  }
  totalCount: number
}

// 业务订单关联API
export const BusinessOrderRelationApi = {
  // 分页查询业务订单关联
  getOrderRelationPage: (params: BusinessOrderRelationPageReq) =>
    request.get({ url: '/business/order-relation/page', params }),

  // 获取业务订单关联详情
  getOrderRelation: (id: number) =>
    request.get({ url: `/business/order-relation/${id}` }),

  // 更新业务订单关联
  updateOrderRelation: (data: BusinessOrderRelationUpdateReq) =>
    request.put({ url: `/business/order-relation/${data.id}`, data }),

  // 按订单ID查询业务关联
  getOrderRelationsByOrderId: (orderId: number) =>
    request.get({ 
      url: '/business/order-relation/list-by-order', 
      params: { orderId } 
    }),

  // 按业务类型查询业务关联
  getOrderRelationsByType: (businessType: string, params?: any) =>
    request.get({ 
      url: '/business/order-relation/list-by-type', 
      params: { businessType, ...params } 
    }),

  // 获取业务状态统计
  getBusinessStatusStatistics: (businessType?: string) =>
    request.get({ 
      url: '/business/order-relation/status-statistics', 
      params: { businessType } 
    }),

  // 批量更新业务状态
  batchUpdateStatus: (ids: number[], businessStatus: string, processResult?: string) =>
    request.put({ 
      url: '/business/order-relation/batch-update-status', 
      data: { ids, businessStatus, processResult } 
    }),

  // 导出业务订单关联数据
  exportOrderRelations: (params: BusinessOrderRelationPageReq) =>
    request.download({ 
      url: '/business/order-relation/export', 
      params 
    })
}

export default BusinessOrderRelationApi
