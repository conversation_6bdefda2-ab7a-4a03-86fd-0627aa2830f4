# Git 提交信息示例

## 📝 提交信息格式

```
<type>(<scope>): <subject>

[body]

[footer]
```

## 🎯 常用提交示例

### 新功能 (feat)

```bash
# 添加新功能
git commit -m "feat(user): 添加用户管理功能"
git commit -m "feat(auth): 实现JWT认证机制"
git commit -m "feat(dashboard): 添加数据统计图表"

# 带作用域的新功能
git commit -m "feat(api): 新增用户API接口"
git commit -m "feat(ui): 添加响应式导航组件"
```

### 修复问题 (fix)

```bash
# 修复 bug
git commit -m "fix(login): 修复登录页面样式问题"
git commit -m "fix(api): 修复用户数据获取异常"
git commit -m "fix(table): 修复表格分页显示错误"

# 修复安全问题
git commit -m "fix(security): 修复XSS安全漏洞"
```

### 文档更新 (docs)

```bash
# 更新文档
git commit -m "docs(readme): 更新项目安装说明"
git commit -m "docs(api): 完善API接口文档"
git commit -m "docs(guide): 添加开发指南"
```

### 代码格式 (style)

```bash
# 代码格式调整
git commit -m "style(button): 调整按钮样式"
git commit -m "style(global): 统一代码缩进格式"
git commit -m "style(header): 优化页面头部布局"
```

### 代码重构 (refactor)

```bash
# 重构代码
git commit -m "refactor(user): 重构用户服务模块"
git commit -m "refactor(utils): 优化工具函数结构"
git commit -m "refactor(components): 抽取公共组件"
```

### 性能优化 (perf)

```bash
# 性能优化
git commit -m "perf(table): 优化大数据表格渲染性能"
git commit -m "perf(image): 添加图片懒加载功能"
git commit -m "perf(bundle): 减少打包体积"
```

### 测试相关 (test)

```bash
# 添加测试
git commit -m "test(user): 添加用户模块单元测试"
git commit -m "test(api): 完善API接口测试用例"
git commit -m "test(utils): 增加工具函数测试覆盖"
```

### 构建相关 (chore)

```bash
# 依赖更新
git commit -m "chore(deps): 更新Vue到最新版本"
git commit -m "chore(deps): 升级Element Plus组件库"

# 配置更新
git commit -m "chore(config): 更新ESLint配置规则"
git commit -m "chore(build): 优化Vite构建配置"
```

### CI/CD 相关 (ci)

```bash
# CI/CD 配置
git commit -m "ci(github): 添加GitHub Actions工作流"
git commit -m "ci(deploy): 更新部署脚本"
git commit -m "ci(test): 配置自动化测试流程"
```

## 🔄 复杂提交示例

### 带详细描述的提交

```bash
git commit -m "feat(user): 添加用户权限管理功能

- 实现基于角色的权限控制
- 添加权限分配界面
- 支持动态权限验证

Closes #123"
```

### 破坏性变更

```bash
git commit -m "feat(api): 重构用户API接口

BREAKING CHANGE: 用户API接口路径从 /api/user 变更为 /api/v2/users

- 更新所有用户相关接口
- 添加API版本控制
- 保持向后兼容性

Closes #456"
```

## ❌ 错误示例

```bash
# ❌ 不规范的提交信息
git commit -m "fix bug"
git commit -m "update"
git commit -m "修复问题"
git commit -m "add new feature"

# ❌ 格式错误
git commit -m "Fix: login page style"  # type 应该小写
git commit -m "feat login: add user management"  # 缺少括号
git commit -m "feat(user) add user management"  # 缺少冒号
```

## ✅ 正确示例

```bash
# ✅ 规范的提交信息
git commit -m "fix(login): 修复登录页面样式问题"
git commit -m "feat(user): 添加用户管理功能"
git commit -m "docs(readme): 更新项目文档"
git commit -m "style(button): 调整按钮样式"
```

## 🛠️ 使用工具

### 1. 交互式提交

```bash
# 使用 commitizen 进行交互式提交
pnpm commit
```

### 2. 提交信息模板

创建 `.gitmessage` 文件作为提交模板：

```
# <type>(<scope>): <subject>
# 
# <body>
# 
# <footer>

# type: feat, fix, docs, style, refactor, perf, test, chore, ci, build, revert
# scope: 影响范围，如 user, auth, api, ui 等
# subject: 简短描述，不超过50个字符
# body: 详细描述（可选）
# footer: 关联的 issue 或 breaking change（可选）
```

然后配置 Git 使用模板：

```bash
git config commit.template .gitmessage
```

## 📚 参考资料

- [Conventional Commits 规范](https://www.conventionalcommits.org/)
- [Angular 提交信息规范](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit)
- [Commitizen 工具](https://github.com/commitizen/cz-cli)
