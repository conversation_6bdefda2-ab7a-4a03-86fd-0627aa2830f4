import request from '@/config/axios'

// 转账单 VO
export interface TransferVO {
  id?: number
  appId?: number
  channelCode?: string
  merchantTransferId?: string
  type?: number
  price?: number
  subject?: string
  userName?: string
  alipayLogonId?: string
  openid?: string
  userAccount?: string
  no?: string
  channelTransferNo?: string
  status?: number
  successTime?: Date
  createTime?: Date
}

// 查询转账单列表
export const getTransferPage = async (params: PageParam) => {
  return await request.get({ url: `/pay/transfer/page`, params })
}

// 查询转账单详情
export const getTransfer = async (id: number) => {
  return await request.get({ url: '/pay/transfer/get?id=' + id })
}

// 创建转账单
export const createTransfer = async (data: TransferVO) => {
  return await request.post({ url: '/pay/transfer/create', data })
}

// 导出转账单
export const exportTransfer = async (params: PageParam) => {
  return await request.download({ url: '/pay/transfer/export-excel', params })
}
