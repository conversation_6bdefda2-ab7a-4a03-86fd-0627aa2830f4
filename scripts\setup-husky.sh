#!/bin/bash

# Husky 安装和配置脚本
# 用于初始化项目的 Git hooks

echo "🚀 开始配置 Husky Git hooks..."

# 检查是否在 Git 仓库中
if [ ! -d ".git" ]; then
  echo "❌ 错误：当前目录不是 Git 仓库，请先初始化 Git 仓库"
  echo "   运行: git init"
  exit 1
fi

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
  echo "❌ 错误：未找到 Node.js，请先安装 Node.js"
  exit 1
fi

# 检查是否安装了 pnpm
if ! command -v pnpm &> /dev/null; then
  echo "❌ 错误：未找到 pnpm，请先安装 pnpm"
  echo "   运行: npm install -g pnpm"
  exit 1
fi

# 安装依赖
echo "📦 安装项目依赖..."
pnpm install

# 初始化 Husky
echo "🔧 初始化 Husky..."
npx husky install

# 设置 hooks 文件权限（Unix 系统）
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
  echo "🔐 设置 hooks 文件权限..."
  chmod +x .husky/pre-commit
  chmod +x .husky/commit-msg
  chmod +x .husky/pre-push
  chmod +x .husky/_/husky.sh
fi

echo "✅ Husky 配置完成！"
echo ""
echo "📋 已配置的 Git hooks："
echo "   • pre-commit:  代码检查和格式化"
echo "   • commit-msg:  提交信息格式检查"
echo "   • pre-push:    推送前完整检查"
echo ""
echo "💡 使用提示："
echo "   • 使用 'npm run commit' 进行交互式提交"
echo "   • 提交信息格式: <type>(<scope>): <subject>"
echo "   • 示例: feat(user): 添加用户管理功能"
echo ""
echo "🎉 现在你可以开始开发了！"
