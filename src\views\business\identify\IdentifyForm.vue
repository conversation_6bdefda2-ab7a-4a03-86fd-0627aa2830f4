<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="认证类型" prop="identifyType">
        <el-select v-model="formData.identifyType" placeholder="请选择认证类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.IDENTIFY_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="企业类型" prop="enterpriseType">
        <el-select v-model="formData.enterpriseType" placeholder="请选择企业类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="证件类型" prop="idType">
        <el-select v-model="formData.idType" placeholder="请选择证件类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="真实名称" prop="idName">
        <el-input v-model="formData.idName" placeholder="请输入真实名称" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="法人名称" prop="legalName">
        <el-input v-model="formData.legalName" placeholder="请输入法人名称" />
      </el-form-item>
      <el-form-item label="证件号码" prop="idNo">
        <el-input v-model="formData.idNo" placeholder="请输入证件号码" />
      </el-form-item>
      <el-form-item label="人像面身份证" prop="faceCard">
        <el-input v-model="formData.faceCard" placeholder="请输入人像面身份证" />
      </el-form-item>
      <el-form-item label="背面身份证" prop="backCard">
        <el-input v-model="formData.backCard" placeholder="请输入背面身份证" />
      </el-form-item>
      <el-form-item label="营业执照" prop="license">
        <el-input v-model="formData.license" placeholder="请输入营业执照" />
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-input v-model="formData.country" placeholder="请输入国家" />
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input v-model="formData.province" placeholder="请输入省份" />
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input v-model="formData.city" placeholder="请输入城市" />
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="注册地" prop="registrationPlace">
        <el-input v-model="formData.registrationPlace" placeholder="请输入注册地" />
      </el-form-item>
      <el-form-item label="注册区域" prop="registrationArea">
        <el-input v-model="formData.registrationArea" placeholder="请输入注册区域" />
      </el-form-item>
      <el-form-item label="主营业务/业务范围" prop="businessScope">
        <el-input v-model="formData.businessScope" placeholder="请输入主营业务/业务范围" />
      </el-form-item>
      <el-form-item label="企业电话" prop="enterprisePhone">
        <el-input v-model="formData.enterprisePhone" placeholder="请输入企业电话" />
      </el-form-item>
      <el-form-item label="企业邮箱" prop="enterpriseEmail">
        <el-input v-model="formData.enterpriseEmail" placeholder="请输入企业邮箱" />
      </el-form-item>
      <el-form-item label="营业期限" prop="businessTerm">
        <el-input v-model="formData.businessTerm" placeholder="请输入营业期限" />
      </el-form-item>
      <el-form-item label="状态" prop="statusCd">
        <el-select v-model="formData.statusCd" placeholder="请选择状态">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.GENERAL_STATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态时间" prop="statusDate">
        <el-date-picker
          v-model="formData.statusDate"
          type="date"
          value-format="x"
          placeholder="选择状态时间"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createStaff">
        <el-input v-model="formData.createStaff" placeholder="请输入创建人" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          v-model="formData.createDate"
          type="date"
          value-format="x"
          placeholder="选择创建时间"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="文创链用户ID" prop="wcUserId">
        <el-input v-model="formData.wcUserId" placeholder="请输入文创链用户ID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { IdentifyApi, IdentifyVO } from '@/api/business/identify'

/** 实名认证 表单 */
defineOptions({ name: 'IdentifyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  identifyId: undefined,
  userId: undefined,
  identifyType: undefined,
  enterpriseType: undefined,
  idType: undefined,
  idName: undefined,
  phone: undefined,
  legalName: undefined,
  idNo: undefined,
  faceCard: undefined,
  backCard: undefined,
  license: undefined,
  country: undefined,
  province: undefined,
  city: undefined,
  address: undefined,
  registrationPlace: undefined,
  registrationArea: undefined,
  businessScope: undefined,
  enterprisePhone: undefined,
  enterpriseEmail: undefined,
  businessTerm: undefined,
  statusCd: undefined,
  statusDate: undefined,
  createStaff: undefined,
  createDate: undefined,
  remark: undefined,
  wcUserId: undefined,
})
const formRules = reactive({
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  identifyType: [{ required: true, message: '认证类型不能为空', trigger: 'change' }],
  idName: [{ required: true, message: '真实名称不能为空', trigger: 'blur' }],
  phone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  idNo: [{ required: true, message: '证件号码不能为空', trigger: 'blur' }],
  country: [{ required: true, message: '国家不能为空', trigger: 'blur' }],
  province: [{ required: true, message: '省份不能为空', trigger: 'blur' }],
  city: [{ required: true, message: '城市不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
  statusCd: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  statusDate: [{ required: true, message: '状态时间不能为空', trigger: 'blur' }],
  createStaff: [{ required: true, message: '创建人不能为空', trigger: 'blur' }],
  createDate: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await IdentifyApi.getIdentify(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as IdentifyVO
    if (formType.value === 'create') {
      await IdentifyApi.createIdentify(data)
      message.success(t('common.createSuccess'))
    } else {
      await IdentifyApi.updateIdentify(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    identifyId: undefined,
    userId: undefined,
    identifyType: undefined,
    enterpriseType: undefined,
    idType: undefined,
    idName: undefined,
    phone: undefined,
    legalName: undefined,
    idNo: undefined,
    faceCard: undefined,
    backCard: undefined,
    license: undefined,
    country: undefined,
    province: undefined,
    city: undefined,
    address: undefined,
    registrationPlace: undefined,
    registrationArea: undefined,
    businessScope: undefined,
    enterprisePhone: undefined,
    enterpriseEmail: undefined,
    businessTerm: undefined,
    statusCd: undefined,
    statusDate: undefined,
    createStaff: undefined,
    createDate: undefined,
    remark: undefined,
    wcUserId: undefined,
  }
  formRef.value?.resetFields()
}
</script>