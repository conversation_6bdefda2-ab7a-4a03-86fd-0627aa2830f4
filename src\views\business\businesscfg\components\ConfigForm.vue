<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="config-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="业务类型" prop="businessType">
            <el-select
              v-model="formData.businessType"
              placeholder="请选择业务类型"
              style="width: 100%"
              :disabled="formType === 'update'"
              @change="handleBusinessTypeChange"
            >
              <el-option
                v-for="option in businessTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <div class="flex items-center">
                  <Icon :icon="option.icon" class="mr-2" />
                  <div>
                    <div>{{ option.label }}</div>
                    <div class="text-xs text-gray-500">{{ option.description }}</div>
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="配置名称" prop="configName">
            <el-input
              v-model="formData.configName"
              placeholder="请输入配置名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联商品" prop="productSpuId">
            <el-select
              v-model="formData.productSpuId"
              placeholder="请选择关联商品"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchProducts"
              :loading="productLoading"
              @change="handleProductChange"
            >
              <el-option
                v-for="product in productOptions"
                :key="product.id"
                :label="`${product.name} - ${formatPrice(product.price)}`"
                :value="product.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="处理天数" prop="processingDays">
            <el-input-number
              v-model="formData.processingDays"
              :min="1"
              :max="365"
              placeholder="请输入处理天数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="服务描述" prop="serviceDescription">
        <el-input
          v-model="formData.serviceDescription"
          type="textarea"
          :rows="3"
          placeholder="请输入服务描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="排序值" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              :max="9999"
              placeholder="请输入排序值"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="默认配置">
        <el-switch
          v-model="formData.isDefault"
          active-text="是"
          inactive-text="否"
          :disabled="isDefaultDisabled"
        />
        <div v-if="isDefaultDisabled" class="text-xs text-gray-500 mt-1">
          每个业务类型只能有一个默认配置
        </div>
      </el-form-item>
      
      <!-- 价格预览 -->
      <el-form-item v-if="selectedProduct" label="价格预览">
        <div class="price-preview">
          <div class="price-item">
            <span class="label">原价：</span>
            <span class="price">{{ formatPrice(selectedProduct.price) }}</span>
          </div>
          <div v-if="selectedProduct.memberPrices" class="member-prices">
            <div
              v-for="(price, level) in selectedProduct.memberPrices"
              :key="level"
              class="member-price-item"
            >
              <span class="level">{{ getMemberLevelName(level) }}：</span>
              <span class="price">{{ formatPrice(price) }}</span>
              <span class="discount">{{ formatDiscount(calculateDiscountRate(selectedProduct.price, price)) }}</span>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        确定
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts" name="ConfigForm">
import { BusinessConfigApi } from '@/api/business/businesscfg/config'
import { BusinessConfigFormData } from '@/types/business/businesscfg/config'
import {
  getBusinessTypeOptions,
  getBusinessConfigStatusOptions,
  getMemberLevelName,
  formatPrice,
  formatDiscount,
  calculateDiscountRate
} from '@/utils/business/businesscfg/helpers'
import { FORM_RULES } from '@/utils/business/businesscfg/constants'

// 定义事件
const emit = defineEmits(['success'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formType = ref<'create' | 'update'>('create')
const submitLoading = ref(false)
const productLoading = ref(false)

// 表单数据
const formData = reactive<BusinessConfigFormData>({
  businessType: '',
  productSpuId: 0,
  configName: '',
  serviceDescription: '',
  processingDays: 1,
  isDefault: false,
  status: 'ACTIVE',
  sortOrder: 1
})

// 表单引用
const formRef = ref()

// 选项数据
const businessTypeOptions = getBusinessTypeOptions()
const statusOptions = getBusinessConfigStatusOptions()
const productOptions = ref<any[]>([])
const selectedProduct = ref<any>(null)

// 表单验证规则
const formRules = FORM_RULES

// 计算属性
const isDefaultDisabled = computed(() => {
  // 如果是更新模式且当前已经是默认配置，则不能取消默认
  return formType.value === 'update' && formData.isDefault
})

// 打开弹窗
const open = async (type: 'create' | 'update', id?: number) => {
  formType.value = type
  dialogTitle.value = type === 'create' ? '新增配置' : '编辑配置'
  dialogVisible.value = true
  
  // 重置表单
  resetForm()
  
  if (type === 'update' && id) {
    await loadConfigData(id)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    businessType: '',
    productSpuId: 0,
    configName: '',
    serviceDescription: '',
    processingDays: 1,
    isDefault: false,
    status: 'ACTIVE',
    sortOrder: 1
  })
  
  productOptions.value = []
  selectedProduct.value = null
  formRef.value?.clearValidate()
}

// 加载配置数据
const loadConfigData = async (id: number) => {
  try {
    const { data } = await BusinessConfigApi.getConfig(id)
    Object.assign(formData, data)
    
    // 加载商品信息
    if (data.productSpuId) {
      await loadProductInfo(data.productSpuId)
    }
  } catch (error) {
    console.error('加载配置数据失败:', error)
  }
}

// 加载商品信息
const loadProductInfo = async (productId: number) => {
  try {
    // 这里应该调用商品API获取商品信息
    // 暂时模拟数据
    const mockProduct = {
      id: productId,
      name: '模拟商品',
      price: 10000, // 100.00元
      memberPrices: {
        bronze: 9000,
        silver: 8000,
        gold: 7000
      }
    }
    
    productOptions.value = [mockProduct]
    selectedProduct.value = mockProduct
  } catch (error) {
    console.error('加载商品信息失败:', error)
  }
}

// 业务类型变化
const handleBusinessTypeChange = (businessType: string) => {
  // 根据业务类型设置默认处理天数
  const config = getBusinessTypeOptions().find(opt => opt.value === businessType)
  if (config) {
    // 这里可以根据业务类型设置默认值
  }
}

// 商品变化
const handleProductChange = (productId: number) => {
  const product = productOptions.value.find(p => p.id === productId)
  selectedProduct.value = product
}

// 搜索商品
const searchProducts = async (query: string) => {
  if (!query) return
  
  productLoading.value = true
  try {
    // 这里应该调用商品搜索API
    // 暂时模拟数据
    productOptions.value = [
      {
        id: 1,
        name: `搜索商品-${query}`,
        price: 10000,
        memberPrices: {
          bronze: 9000,
          silver: 8000,
          gold: 7000
        }
      }
    ]
  } catch (error) {
    console.error('搜索商品失败:', error)
  } finally {
    productLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  submitLoading.value = true
  try {
    if (formType.value === 'create') {
      await BusinessConfigApi.createConfig(formData)
      ElMessage.success('创建成功')
    } else {
      await BusinessConfigApi.updateConfig(formData as any)
      ElMessage.success('更新成功')
    }
    
    handleClose()
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.config-form {
  padding: 20px;
}

.price-preview {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.price-item {
  margin-bottom: 8px;
}

.member-prices {
  margin-top: 12px;
}

.member-price-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.label,
.level {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.price {
  font-weight: 600;
  color: #e74c3c;
  margin-right: 8px;
}

.discount {
  font-size: 12px;
  color: #27ae60;
  background: #e8f5e8;
  padding: 2px 6px;
  border-radius: 2px;
}
</style>
