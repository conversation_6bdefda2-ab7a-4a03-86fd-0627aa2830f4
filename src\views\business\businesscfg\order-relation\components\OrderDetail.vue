<template>
  <Dialog
    v-model="dialogVisible"
    title="订单详情"
    width="900px"
    @close="handleClose"
  >
    <div v-if="orderData" class="order-detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单编号：</span>
              <span class="value">{{ orderData.orderInfo?.orderNo }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单金额：</span>
              <span class="value price">{{ formatPrice(orderData.orderInfo?.totalAmount || 0) }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付状态：</span>
              <el-tag
                :type="orderData.orderInfo?.payStatus === 'PAID' ? 'success' : 'warning'"
                size="small"
              >
                {{ orderData.orderInfo?.payStatus === 'PAID' ? '已支付' : '未支付' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">用户ID：</span>
              <span class="value">{{ orderData.orderInfo?.userId }}</span>
            </div>
            <div class="info-item">
              <span class="label">用户名称：</span>
              <span class="value">{{ orderData.orderInfo?.userName || '未知用户' }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付时间：</span>
              <span class="value">{{ orderData.orderInfo?.payTime ? formatDate(orderData.orderInfo.payTime) : '未支付' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 业务信息 -->
      <div class="detail-section">
        <h3 class="section-title">业务信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">业务类型：</span>
              <div class="value">
                <Icon :icon="getBusinessTypeConfig(orderData.businessType)?.icon" class="mr-2" />
                {{ getBusinessTypeName(orderData.businessType) }}
              </div>
            </div>
            <div class="info-item">
              <span class="label">配置名称：</span>
              <span class="value">{{ orderData.configInfo?.configName }}</span>
            </div>
            <div class="info-item">
              <span class="label">处理天数：</span>
              <span class="value">{{ orderData.configInfo?.processingDays }}天</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">业务状态：</span>
              <el-tag
                :type="getBusinessStatusConfig(orderData.businessStatus)?.color"
                size="small"
              >
                {{ getBusinessStatusName(orderData.businessStatus) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDate(orderData.createTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ formatDate(orderData.updateTime) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div v-if="orderData.configInfo?.serviceDescription" class="info-item full-width">
          <span class="label">服务描述：</span>
          <div class="value description">{{ orderData.configInfo.serviceDescription }}</div>
        </div>
      </div>

      <!-- 业务数据 -->
      <div v-if="businessDataItems.length" class="detail-section">
        <h3 class="section-title">业务数据</h3>
        <div class="business-data">
          <el-table :data="businessDataItems" border size="small">
            <el-table-column label="字段名称" prop="label" width="150" />
            <el-table-column label="字段值" prop="value">
              <template #default="{ row }">
                <div v-if="row.type === 'json'" class="json-data">
                  <el-button type="primary" link @click="viewJsonData(row.value)">
                    查看JSON数据
                  </el-button>
                </div>
                <div v-else-if="row.type === 'image'" class="image-data">
                  <el-image
                    :src="row.value"
                    :preview-src-list="[row.value]"
                    style="width: 60px; height: 60px"
                    fit="cover"
                  />
                </div>
                <div v-else-if="row.type === 'file'" class="file-data">
                  <el-button type="primary" link @click="downloadFile(row.value)">
                    <Icon icon="ep:download" class="mr-1" />
                    下载文件
                  </el-button>
                </div>
                <div v-else-if="row.type === 'date'" class="date-data">
                  {{ formatDate(row.value) }}
                </div>
                <div v-else class="text-data">
                  {{ row.value }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 处理结果 -->
      <div v-if="orderData.processResult" class="detail-section">
        <h3 class="section-title">处理结果</h3>
        <div class="process-result">
          <el-input
            :model-value="orderData.processResult"
            type="textarea"
            :rows="4"
            readonly
            placeholder="暂无处理结果"
          />
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="detail-section">
        <h3 class="section-title">操作日志</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(log, index) in operationLogs"
            :key="index"
            :timestamp="log.time"
            :type="log.type"
            :icon="log.icon"
          >
            <div class="log-content">
              <div class="log-title">{{ log.title }}</div>
              <div v-if="log.description" class="log-description">{{ log.description }}</div>
              <div v-if="log.operator" class="log-operator">操作人：{{ log.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handlePrint">
        <Icon icon="ep:printer" class="mr-1" />
        打印
      </el-button>
    </template>

    <!-- JSON数据查看弹窗 -->
    <Dialog
      v-model="jsonDialogVisible"
      title="JSON数据"
      width="600px"
    >
      <el-input
        :model-value="jsonData"
        type="textarea"
        :rows="15"
        readonly
      />
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts" name="OrderDetail">
import { BusinessOrderRelationTableItem } from '@/types/business/businesscfg/order-relation'
import { BusinessDataItem } from '@/types/business/businesscfg/order-relation'
import {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getBusinessStatusName,
  getBusinessStatusConfig,
  formatPrice,
  formatBusinessData
} from '@/utils/business/businesscfg/helpers'
import { formatDate } from '@/utils/formatTime'

// 响应式数据
const dialogVisible = ref(false)
const jsonDialogVisible = ref(false)
const orderData = ref<BusinessOrderRelationTableItem | null>(null)
const businessDataItems = ref<BusinessDataItem[]>([])
const jsonData = ref('')

// 操作日志数据
const operationLogs = ref([
  {
    time: '2024-01-15 10:00:00',
    type: 'primary',
    icon: 'ep:plus',
    title: '订单创建',
    description: '用户提交业务申请',
    operator: '系统'
  },
  {
    time: '2024-01-15 10:05:00',
    type: 'warning',
    icon: 'ep:clock',
    title: '等待处理',
    description: '业务申请进入待处理队列',
    operator: '系统'
  }
])

// 打开弹窗
const open = (data: BusinessOrderRelationTableItem) => {
  orderData.value = data
  dialogVisible.value = true
  
  // 解析业务数据
  if (data.businessData) {
    businessDataItems.value = formatBusinessData(data.businessData)
  } else {
    businessDataItems.value = []
  }
  
  // 加载操作日志
  loadOperationLogs(data.id)
}

// 加载操作日志
const loadOperationLogs = async (id: number) => {
  try {
    // 这里应该调用API获取操作日志
    // 暂时使用模拟数据
    console.log('加载操作日志:', id)
  } catch (error) {
    console.error('加载操作日志失败:', error)
  }
}

// 查看JSON数据
const viewJsonData = (data: any) => {
  jsonData.value = JSON.stringify(data, null, 2)
  jsonDialogVisible.value = true
}

// 下载文件
const downloadFile = (url: string) => {
  window.open(url, '_blank')
}

// 打印
const handlePrint = () => {
  window.print()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  orderData.value = null
  businessDataItems.value = []
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.order-detail-container {
  padding: 20px;
}

.detail-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e8ed;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.info-item.full-width {
  flex-direction: column;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 100px;
  margin-right: 12px;
}

.value {
  flex: 1;
  color: #333;
  display: flex;
  align-items: center;
}

.value.price {
  font-weight: 600;
  color: #e74c3c;
}

.description {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  margin-top: 8px;
}

.business-data {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
}

.json-data,
.image-data,
.file-data,
.date-data,
.text-data {
  padding: 4px 0;
}

.process-result {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
}

.log-content {
  padding: 8px 0;
}

.log-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.log-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.log-operator {
  color: #999;
  font-size: 12px;
}

@media print {
  .order-detail-container {
    padding: 0;
  }
  
  .section-title {
    color: #000 !important;
    border-bottom-color: #000 !important;
  }
}
</style>
