{"recommendations": ["christian-kohler.path-intellisense", "vscode-icons-team.vscode-icons", "davidanson.vscode-markdownlint", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "mrmlnc.vscode-less", "lokalise.i18n-ally", "redhat.vscode-yaml", "csstools.postcss", "mikestead.dotenv", "eamodio.gitlens", "antfu.iconify", "antfu.unocss", "Vue.volar", "ms-vscode.vscode-typescript-next", "ms-vscode.js-debug", "firefox-devtools.vscode-firefox-debug"]}