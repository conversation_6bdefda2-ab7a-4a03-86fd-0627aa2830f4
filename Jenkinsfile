pipeline {
    agent any
    
    environment {
        NODE_OPTIONS = '--max_old_space_size=1536 --max-semi-space-size=64'
        NODE_ENV = 'production'
        VITE_BUILD_CHUNK_SIZE_LIMIT = '800'
        VITE_BUILD_ROLLUP_OPTIONS_EXTERNAL = 'true'
    }
    
    stages {
        stage('环境检查') {
            steps {
                script {
                    echo '🔍 检查环境信息...'
                    sh 'node --version'
                    sh 'npm --version'
                    sh 'free -h'
                    sh 'df -h'
                }
            }
        }
        
        stage('清理环境') {
            steps {
                script {
                    echo '🧹 清理构建环境...'
                    sh 'rm -rf node_modules/.cache || true'
                    sh 'rm -rf dist-prod || true'
                    sh 'rm -rf .vite || true'
                    sh 'npm cache clean --force || true'
                }
            }
        }
        
        stage('安装依赖') {
            steps {
                script {
                    echo '📦 安装项目依赖...'
                    // 使用 npm ci 而不是 npm install，更适合 CI 环境
                    sh 'npm ci --production=false --silent'
                }
            }
        }
        
        stage('构建项目') {
            steps {
                script {
                    echo '🔨 开始构建项目...'
                    // 使用专门的 Jenkins 构建命令
                    sh 'npm run build:jenkins'
                }
            }
        }
        
        stage('验证构建') {
            steps {
                script {
                    echo '✅ 验证构建结果...'
                    sh '''
                        if [ -d "dist-prod" ]; then
                            echo "构建成功！"
                            echo "构建产物大小: $(du -sh dist-prod)"
                            echo "文件数量: $(find dist-prod -type f | wc -l)"
                        else
                            echo "构建失败！"
                            exit 1
                        fi
                    '''
                }
            }
        }
        
        stage('归档产物') {
            steps {
                script {
                    echo '📁 归档构建产物...'
                    archiveArtifacts artifacts: 'dist-prod/**/*', fingerprint: true
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo '🧹 清理工作空间...'
                sh 'rm -rf node_modules/.cache || true'
                sh 'rm -rf .vite || true'
            }
        }
        success {
            echo '🎉 构建成功完成！'
        }
        failure {
            echo '❌ 构建失败！'
        }
    }
}
