name: 上游同步检查

on:
  schedule:
    # 每周一上午9点检查上游更新
    - cron: '0 9 * * 1'
  workflow_dispatch:
    # 支持手动触发
    inputs:
      sync_type:
        description: '同步类型'
        required: true
        default: 'check'
        type: choice
        options:
          - check      # 仅检查
          - safe       # 安全同步
          - full       # 完整同步（需要人工确认）

jobs:
  check-upstream:
    runs-on: ubuntu-latest
    outputs:
      has-updates: ${{ steps.check.outputs.has-updates }}
      commit-count: ${{ steps.check.outputs.commit-count }}
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 配置 Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: 添加上游仓库
        run: |
          git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git || true
          git fetch upstream

      - name: 检查上游更新
        id: check
        run: |
          COMMIT_COUNT=$(git rev-list HEAD..upstream/master --count)
          echo "commit-count=$COMMIT_COUNT" >> $GITHUB_OUTPUT
          
          if [ "$COMMIT_COUNT" -gt 0 ]; then
            echo "has-updates=true" >> $GITHUB_OUTPUT
            echo "发现 $COMMIT_COUNT 个新提交"
            
            echo "## 上游更新日志" >> $GITHUB_STEP_SUMMARY
            git log --oneline HEAD..upstream/master >> $GITHUB_STEP_SUMMARY
          else
            echo "has-updates=false" >> $GITHUB_OUTPUT
            echo "无新更新"
          fi

  safe-sync:
    needs: check-upstream
    if: needs.check-upstream.outputs.has-updates == 'true' && (github.event.inputs.sync_type == 'safe' || github.event.inputs.sync_type == 'full')
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: 安装依赖
        run: pnpm install

      - name: 配置 Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: 添加上游仓库
        run: |
          git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git || true
          git fetch upstream

      - name: 执行安全同步
        run: |
          # 创建同步分支
          SYNC_BRANCH="sync/upstream-$(date +%Y%m%d-%H%M%S)"
          git checkout -b $SYNC_BRANCH
          
          # 执行同步脚本
          node scripts/sync-modules.js
          
          echo "SYNC_BRANCH=$SYNC_BRANCH" >> $GITHUB_ENV

      - name: 运行测试
        run: |
          # TypeScript 检查
          pnpm run ts:check
          
          # 代码检查
          pnpm run lint:eslint
          pnpm run lint:style
          
          # 构建测试
          pnpm run build:test

      - name: 创建 Pull Request
        if: success()
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ env.SYNC_BRANCH }}
          title: "🔄 上游同步: ${{ needs.check-upstream.outputs.commit-count }} 个新提交"
          body: |
            ## 📋 上游同步报告
            
            本 PR 包含来自芋道源码的 ${{ needs.check-upstream.outputs.commit-count }} 个新提交的安全同步。
            
            ### ✅ 已同步的模块
            - 基础组件
            - 工具函数
            - 类型定义
            - Hooks
            - 指令
            
            ### ⚠️ 需要手动检查的模块
            - 系统管理模块
            - 基础设施模块
            - 构建配置
            - 依赖版本
            
            ### 🧪 自动化测试结果
            - ✅ TypeScript 检查通过
            - ✅ ESLint 检查通过
            - ✅ 样式检查通过
            - ✅ 构建测试通过
            
            ### 📝 注意事项
            1. 请仔细检查变更内容
            2. 测试相关功能是否正常
            3. 确认无业务逻辑冲突
            4. 如需同步其他模块，请手动操作
            
            ---
            *此 PR 由自动化工作流创建*
          labels: |
            upstream-sync
            auto-generated
          reviewers: |
            # 添加需要审查的团队成员
            # team-lead
            # senior-dev

  notify:
    needs: [check-upstream, safe-sync]
    if: always()
    runs-on: ubuntu-latest
    
    steps:
      - name: 发送通知
        run: |
          if [ "${{ needs.check-upstream.outputs.has-updates }}" == "true" ]; then
            echo "📢 发现上游更新，已创建同步 PR"
            # 这里可以添加其他通知方式，如 Slack、邮件等
          else
            echo "✅ 上游无新更新"
          fi
