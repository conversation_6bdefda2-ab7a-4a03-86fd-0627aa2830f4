{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "启动：本地开发环境",
      "type": "shell",
      "command": "npm",
      "args": ["run", "dev"],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "启动本地开发服务器 (端口: 3000, 环境: .env.local)",
      "isBackground": true,
      "runOptions": {
        "instanceLimit": 1
      }
    },
    {
      "label": "启动：开发服务器环境",
      "type": "shell",
      "command": "npm",
      "args": ["run", "dev-server"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "启动开发服务器环境 (端口: 3000, 环境: .env.dev)",
      "isBackground": true,
      "runOptions": {
        "instanceLimit": 1
      }
    },
    {
      "label": "构建：本地环境",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build:local"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "构建本地环境版本"
    },
    {
      "label": "构建：开发环境",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build:dev"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "构建开发环境版本"
    },
    {
      "label": "构建：生产环境",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build:prod"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "构建生产环境版本"
    },
    {
      "label": "预览：本地构建",
      "type": "shell",
      "command": "npm",
      "args": ["run", "preview"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "预览本地构建结果"
    },
    {
      "label": "代码检查：ESLint",
      "type": "shell",
      "command": "npm",
      "args": ["run", "lint:eslint"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": ["$eslint-stylish"],
      "detail": "运行 ESLint 代码检查"
    },
    {
      "label": "代码格式化：Prettier",
      "type": "shell",
      "command": "npm",
      "args": ["run", "lint:format"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "运行 Prettier 代码格式化"
    },
    {
      "label": "TypeScript 检查",
      "type": "shell",
      "command": "npm",
      "args": ["run", "ts:check"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": ["$tsc"],
      "detail": "运行 TypeScript 类型检查"
    },
    {
      "label": "等待服务器启动",
      "type": "shell",
      "command": "node",
      "args": ["scripts/wait-for-server.js", "3000"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "silent",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": false,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": [],
      "detail": "等待开发服务器完全启动"
    }
  ]
}
