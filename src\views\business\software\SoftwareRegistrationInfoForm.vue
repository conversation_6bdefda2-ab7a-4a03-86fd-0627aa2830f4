<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="软著登记类型" prop="softwareType">
        <el-select v-model="formData.softwareType" placeholder="请选择软著登记类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="取得方式" prop="acquisitionMethod">
        <el-select v-model="formData.acquisitionMethod" placeholder="请选择取得方式">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_ACQUISITION_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="完整名称" prop="softwareFullName">
        <el-input v-model="formData.softwareFullName" placeholder="请输入完整名称" />
      </el-form-item>
      <el-form-item label="版本编号" prop="versionNumber">
        <el-input v-model="formData.versionNumber" placeholder="请输入版本编号" />
      </el-form-item>
      <el-form-item label="权利范围" prop="rightsScope">
        <el-select v-model="formData.rightsScope" placeholder="请选择权利范围">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_RIGHTS_SCOPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="平台分类" prop="platformType">
        <el-select v-model="formData.platformType" placeholder="请选择平台分类">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_PLATFORM_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软件分类" prop="softwareClassification">
        <el-select v-model="formData.softwareClassification" placeholder="请选择软件分类">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_CLASSIFICATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软件说明" prop="softwareDescription">
        <el-select v-model="formData.softwareDescription" placeholder="请选择软件说明">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DESCRIPTION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开发方式" prop="developmentMethod">
        <el-select v-model="formData.developmentMethod" placeholder="请选择开发方式">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DEVELOPMENT_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="编程语言" prop="programmingLanguages">
        <el-select v-model="formData.programmingLanguages" placeholder="请选择编程语言">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_PROGRAMMING_LANGUAGES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="编程语言描述" prop="programmingLanguagesDescribe">
        <el-input v-model="formData.programmingLanguagesDescribe" placeholder="请输入编程语言描述" />
      </el-form-item>
      <el-form-item label="开发完成日期" prop="completionDate">
        <el-date-picker
          v-model="formData.completionDate"
          type="date"
          value-format="x"
          placeholder="选择开发完成日期"
        />
      </el-form-item>
      <el-form-item label="登记人员邮箱" prop="registrantEmail">
        <el-input v-model="formData.registrantEmail" placeholder="请输入登记人员邮箱" />
      </el-form-item>
      <el-form-item label="登记人员手机号" prop="registrantPhone">
        <el-input v-model="formData.registrantPhone" placeholder="请输入登记人员手机号" />
      </el-form-item>
      <el-form-item label="详细介绍内容" prop="softwareIntroduction">
        <el-input v-model="formData.softwareIntroduction" placeholder="请输入详细介绍内容" />
      </el-form-item>
      <el-form-item label="软著发表状态" prop="publishStatus">
        <el-select v-model="formData.publishStatus" placeholder="请选择软著发表状态">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开发的硬件环境" prop="developmentHardwareEnvironment">
        <el-input v-model="formData.developmentHardwareEnvironment" placeholder="请输入开发的硬件环境" />
      </el-form-item>
      <el-form-item label="运行的硬件环境" prop="runningHardwareEnvironment">
        <el-input v-model="formData.runningHardwareEnvironment" placeholder="请输入运行的硬件环境" />
      </el-form-item>
      <el-form-item label="开发该软件的操作系统" prop="developmentOs">
        <el-input v-model="formData.developmentOs" placeholder="请输入开发该软件的操作系统" />
      </el-form-item>
      <el-form-item label="软件开发环境/开发工具" prop="developmentTools">
        <el-input v-model="formData.developmentTools" placeholder="请输入软件开发环境/开发工具" />
      </el-form-item>
      <el-form-item label="该软件的运行平台/操作系统" prop="runningPlatformOs">
        <el-input v-model="formData.runningPlatformOs" placeholder="请输入该软件的运行平台/操作系统" />
      </el-form-item>
      <el-form-item label="软件运行支撑环境/支持软件" prop="runningSupportEnvironment">
        <el-input v-model="formData.runningSupportEnvironment" placeholder="请输入软件运行支撑环境/支持软件" />
      </el-form-item>
      <el-form-item label="源程序的行数" prop="sourceCodeLines">
        <el-input v-model="formData.sourceCodeLines" placeholder="请输入源程序的行数" />
      </el-form-item>
      <el-form-item label="开发目的" prop="developmentPurpose">
        <el-input v-model="formData.developmentPurpose" placeholder="请输入开发目的" />
      </el-form-item>
      <el-form-item label="面向领域/行业" prop="targetDomain">
        <el-input v-model="formData.targetDomain" placeholder="请输入面向领域/行业" />
      </el-form-item>
      <el-form-item label="软件的主要功能" prop="mainFunction">
        <el-input v-model="formData.mainFunction" placeholder="请输入软件的主要功能" />
      </el-form-item>
      <el-form-item label="软件的技术特点，多种特点以逗号分隔" prop="technicalFeatures">
        <el-select v-model="formData.technicalFeatures" placeholder="请选择软件的技术特点，多种特点以逗号分隔">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_TECHNICAL_FEATURES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软件技术特点描述" prop="technicalFeaturesDescribe">
        <el-input v-model="formData.technicalFeaturesDescribe" placeholder="请输入软件技术特点描述" />
      </el-form-item>
      <el-form-item label="程序鉴别材料的提交方式" prop="programIdentificationType">
        <el-select v-model="formData.programIdentificationType" placeholder="请选择程序鉴别材料的提交方式">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DEPOSIT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="程序鉴别材料ID，多个以逗号隔开" prop="programIdentificationIds">
        <el-input v-model="formData.programIdentificationIds" placeholder="请输入程序鉴别材料ID，多个以逗号隔开" />
      </el-form-item>
      <el-form-item label="文档鉴别材料的提交方式" prop="documentIdentificationType">
        <el-select v-model="formData.documentIdentificationType" placeholder="请选择文档鉴别材料的提交方式">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DEPOSIT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文档鉴别材料ID，多个以逗号隔开" prop="documentIdentificationIds">
        <el-input v-model="formData.documentIdentificationIds" placeholder="请输入文档鉴别材料ID，多个以逗号隔开" />
      </el-form-item>
      <el-form-item label="其他相关证明文件ID，多个以逗号隔开" prop="otherCertificationIds">
        <el-input v-model="formData.otherCertificationIds" placeholder="请输入其他相关证明文件ID，多个以逗号隔开" />
      </el-form-item>
      <el-form-item label="状态" prop="statusCd">
        <el-input v-model="formData.statusCd" placeholder="请输入状态" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { SoftwareInfoApi, SoftwareInfoVO } from '@/api/business/software'

/** 软件登记信息 表单 */
defineOptions({ name: 'SoftwareInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  softwareType: undefined,
  acquisitionMethod: undefined,
  softwareFullName: undefined,
  versionNumber: undefined,
  rightsScope: undefined,
  platformType: undefined,
  softwareClassification: undefined,
  softwareDescription: undefined,
  developmentMethod: undefined,
  programmingLanguages: undefined,
  programmingLanguagesDescribe: undefined,
  completionDate: undefined,
  registrantEmail: undefined,
  registrantPhone: undefined,
  softwareIntroduction: undefined,
  publishStatus: undefined,
  developmentHardwareEnvironment: undefined,
  runningHardwareEnvironment: undefined,
  developmentOs: undefined,
  developmentTools: undefined,
  runningPlatformOs: undefined,
  runningSupportEnvironment: undefined,
  sourceCodeLines: undefined,
  developmentPurpose: undefined,
  targetDomain: undefined,
  mainFunction: undefined,
  technicalFeatures: undefined,
  technicalFeaturesDescribe: undefined,
  programIdentificationType: undefined,
  programIdentificationIds: undefined,
  documentIdentificationType: undefined,
  documentIdentificationIds: undefined,
  otherCertificationIds: undefined,
  statusCd: undefined,
  remark: undefined,
})
const formRules = reactive({
  softwareType: [{ required: true, message: '软著登记类型不能为空', trigger: 'change' }],
  acquisitionMethod: [{ required: true, message: '取得方式不能为空', trigger: 'change' }],
  softwareFullName: [{ required: true, message: '完整名称不能为空', trigger: 'blur' }],
  versionNumber: [{ required: true, message: '版本编号不能为空', trigger: 'blur' }],
  rightsScope: [{ required: true, message: '权利范围不能为空', trigger: 'change' }],
  platformType: [{ required: true, message: '平台分类不能为空', trigger: 'change' }],
  softwareClassification: [{ required: true, message: '软件分类不能为空', trigger: 'change' }],
  softwareDescription: [{ required: true, message: '软件说明不能为空', trigger: 'change' }],
  developmentMethod: [{ required: true, message: '开发方式不能为空', trigger: 'change' }],
  programmingLanguages: [{ required: true, message: '编程语言不能为空', trigger: 'change' }],
  completionDate: [{ required: true, message: '开发完成日期不能为空', trigger: 'blur' }],
  registrantEmail: [{ required: true, message: '登记人员邮箱不能为空', trigger: 'blur' }],
  registrantPhone: [{ required: true, message: '登记人员手机号不能为空', trigger: 'blur' }],
  softwareIntroduction: [{ required: true, message: '详细介绍内容不能为空', trigger: 'blur' }],
  publishStatus: [{ required: true, message: '软著发表状态不能为空', trigger: 'change' }],
  developmentHardwareEnvironment: [{ required: true, message: '开发的硬件环境不能为空', trigger: 'blur' }],
  runningHardwareEnvironment: [{ required: true, message: '运行的硬件环境不能为空', trigger: 'blur' }],
  developmentOs: [{ required: true, message: '开发该软件的操作系统不能为空', trigger: 'blur' }],
  developmentTools: [{ required: true, message: '软件开发环境/开发工具不能为空', trigger: 'blur' }],
  runningPlatformOs: [{ required: true, message: '该软件的运行平台/操作系统不能为空', trigger: 'blur' }],
  runningSupportEnvironment: [{ required: true, message: '软件运行支撑环境/支持软件不能为空', trigger: 'blur' }],
  sourceCodeLines: [{ required: true, message: '源程序的行数不能为空', trigger: 'blur' }],
  developmentPurpose: [{ required: true, message: '开发目的不能为空', trigger: 'blur' }],
  targetDomain: [{ required: true, message: '面向领域/行业不能为空', trigger: 'blur' }],
  mainFunction: [{ required: true, message: '软件的主要功能不能为空', trigger: 'blur' }],
  technicalFeatures: [{ required: true, message: '软件的技术特点，多种特点以逗号分隔不能为空', trigger: 'change' }],
  programIdentificationType: [{ required: true, message: '程序鉴别材料的提交方式不能为空', trigger: 'change' }],
  programIdentificationIds: [{ required: true, message: '程序鉴别材料ID，多个以逗号隔开不能为空', trigger: 'blur' }],
  documentIdentificationType: [{ required: true, message: '文档鉴别材料的提交方式不能为空', trigger: 'change' }],
  documentIdentificationIds: [{ required: true, message: '文档鉴别材料ID，多个以逗号隔开不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SoftwareInfoApi.getSoftwareInfo(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SoftwareInfoVO
    if (formType.value === 'create') {
      await SoftwareInfoApi.createSoftwareInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await SoftwareInfoApi.updateSoftwareInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    softwareType: undefined,
    acquisitionMethod: undefined,
    softwareFullName: undefined,
    versionNumber: undefined,
    rightsScope: undefined,
    platformType: undefined,
    softwareClassification: undefined,
    softwareDescription: undefined,
    developmentMethod: undefined,
    programmingLanguages: undefined,
    programmingLanguagesDescribe: undefined,
    completionDate: undefined,
    registrantEmail: undefined,
    registrantPhone: undefined,
    softwareIntroduction: undefined,
    publishStatus: undefined,
    developmentHardwareEnvironment: undefined,
    runningHardwareEnvironment: undefined,
    developmentOs: undefined,
    developmentTools: undefined,
    runningPlatformOs: undefined,
    runningSupportEnvironment: undefined,
    sourceCodeLines: undefined,
    developmentPurpose: undefined,
    targetDomain: undefined,
    mainFunction: undefined,
    technicalFeatures: undefined,
    technicalFeaturesDescribe: undefined,
    programIdentificationType: undefined,
    programIdentificationIds: undefined,
    documentIdentificationType: undefined,
    documentIdentificationIds: undefined,
    otherCertificationIds: undefined,
    statusCd: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>
