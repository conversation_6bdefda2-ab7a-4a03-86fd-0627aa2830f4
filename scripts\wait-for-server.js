#!/usr/bin/env node

/**
 * 等待开发服务器启动的脚本
 * 用于确保调试器连接时服务器已经完全启动
 */

const http = require('http');

function checkServer(url, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    function check() {
      const req = http.get(url, (res) => {
        if (res.statusCode === 200) {
          console.log(`✅ 服务器已启动: ${url}`);
          resolve(true);
        } else {
          console.log(`⏳ 服务器响应状态: ${res.statusCode}, 继续等待...`);
          setTimeout(check, 1000);
        }
      });
      
      req.on('error', (err) => {
        const elapsed = Date.now() - startTime;
        if (elapsed > timeout) {
          console.error(`❌ 等待超时 (${timeout}ms): ${url}`);
          reject(new Error(`Server timeout: ${url}`));
        } else {
          console.log(`⏳ 等待服务器启动... (${Math.round(elapsed/1000)}s)`);
          setTimeout(check, 1000);
        }
      });
      
      req.setTimeout(5000, () => {
        req.destroy();
        const elapsed = Date.now() - startTime;
        if (elapsed > timeout) {
          console.error(`❌ 等待超时 (${timeout}ms): ${url}`);
          reject(new Error(`Server timeout: ${url}`));
        } else {
          console.log(`⏳ 连接超时，重试... (${Math.round(elapsed/1000)}s)`);
          setTimeout(check, 1000);
        }
      });
    }
    
    check();
  });
}

async function main() {
  const args = process.argv.slice(2);
  const port = args[0] || '3000';
  const url = `http://localhost:${port}`;
  
  console.log(`🚀 等待服务器启动: ${url}`);
  
  try {
    await checkServer(url);
    console.log(`🎉 服务器已就绪，可以开始调试！`);
    process.exit(0);
  } catch (error) {
    console.error(`❌ 服务器启动失败: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkServer };
