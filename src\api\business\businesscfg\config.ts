import request from '@/config/axios'

// 业务配置分页请求
export interface BusinessConfigPageReq {
  pageNo: number
  pageSize: number
  businessType?: string
  configName?: string
  status?: string
  isDefault?: boolean
}

// 业务配置创建请求
export interface BusinessConfigCreateReq {
  businessType: string
  productSpuId: number
  configName: string
  serviceDescription?: string
  processingDays: number
  isDefault: boolean
  status: string
  sortOrder: number
}

// 业务配置更新请求
export interface BusinessConfigUpdateReq {
  id: number
  businessType: string
  productSpuId: number
  configName: string
  serviceDescription?: string
  processingDays: number
  isDefault: boolean
  status: string
  sortOrder: number
}

// 业务配置响应
export interface BusinessConfigRespVO {
  id: number
  businessType: string
  productSpuId: number
  configName: string
  serviceDescription?: string
  processingDays: number
  isDefault: boolean
  status: string
  sortOrder: number
  createTime: string
  updateTime: string
  // 关联商品信息
  productInfo?: {
    spuId: number
    name: string
    price: number
    memberPrices?: {
      bronze: number
      silver: number
      gold: number
    }
  }
}

// 业务配置API
export const BusinessConfigApi = {
  // 分页查询配置
  getConfigPage: (params: BusinessConfigPageReq) =>
    request.get({ url: '/business/product-config/page', params }),

  // 获取配置详情
  getConfig: (id: number) =>
    request.get({ url: `/business/product-config/get/${id}` }),

  // 创建配置
  createConfig: (data: BusinessConfigCreateReq) =>
    request.post({ url: '/business/product-config/create', data }),

  // 更新配置
  updateConfig: (data: BusinessConfigUpdateReq) =>
    request.put({ url: `/business/product-config/update/${data.id}`, data }),

  // 删除配置
  deleteConfig: (id: number) =>
    request.delete({ url: `/business/product-config/delete/${id}` }),

  // 设置默认配置
  setDefaultConfig: (id: number) =>
    request.put({ url: `/business/product-config/set-default/${id}` }),

  // 按业务类型查询配置列表
  getConfigsByType: (businessType: string) =>
    request.get({ 
      url: '/business/product-config/list-by-type', 
      params: { businessType } 
    }),

  // 获取所有业务类型
  getBusinessTypes: () =>
    request.get({ url: '/business/product-config/business-types' }),

  // 批量更新排序
  updateSort: (data: { id: number; sortOrder: number }[]) =>
    request.put({ url: '/business/product-config/update-sort', data }),

  // 批量更新状态
  updateStatus: (ids: number[], status: string) =>
    request.put({ 
      url: '/business/product-config/update-status', 
      data: { ids, status } 
    })
}

export default BusinessConfigApi
