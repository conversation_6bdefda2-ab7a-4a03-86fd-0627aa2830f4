import request from '@/config/axios'

// 软件登记信息 VO
export interface SoftwareInfoVO {
  id: number // 主键
  softwareType: string // 软著登记类型
  acquisitionMethod: string // 取得方式
  softwareFullName: string // 完整名称
  versionNumber: string // 版本编号
  rightsScope: string // 权利范围
  platformType: string // 平台分类
  softwareClassification: string // 软件分类
  softwareDescription: string // 软件说明
  developmentMethod: string // 开发方式
  programmingLanguages: string // 编程语言
  programmingLanguagesDescribe: string // 编程语言描述
  completionDate: Date // 开发完成日期
  registrantEmail: string // 登记人员邮箱
  registrantPhone: string // 登记人员手机号
  softwareIntroduction: string // 详细介绍内容
  publishStatus: string // 软著发表状态
  developmentHardwareEnvironment: string // 开发的硬件环境
  runningHardwareEnvironment: string // 运行的硬件环境
  developmentOs: string // 开发该软件的操作系统
  developmentTools: string // 软件开发环境/开发工具
  runningPlatformOs: string // 该软件的运行平台/操作系统
  runningSupportEnvironment: string // 软件运行支撑环境/支持软件
  sourceCodeLines: number // 源程序的行数
  developmentPurpose: string // 开发目的
  targetDomain: string // 面向领域/行业
  mainFunction: string // 软件的主要功能
  technicalFeatures: string // 软件的技术特点，多种特点以逗号分隔
  technicalFeaturesDescribe: string // 软件技术特点描述
  programIdentificationType: string // 程序鉴别材料的提交方式
  programIdentificationIds: string // 程序鉴别材料ID，多个以逗号隔开
  documentIdentificationType: string // 文档鉴别材料的提交方式
  documentIdentificationIds: string // 文档鉴别材料ID，多个以逗号隔开
  otherCertificationIds: string // 其他相关证明文件ID，多个以逗号隔开
  statusCd: string // 状态
  remark: string // 备注
}

// 软件登记信息 API
export const SoftwareInfoApi = {
  // 查询软件登记信息分页
  getSoftwareInfoPage: async (params: any) => {
    return await request.get({ url: `/software/registration-info/page`, params })
  },

  // 查询软件登记信息详情
  getSoftwareInfo: async (id: number) => {
    return await request.get({ url: `/software/registration-info/get?id=` + id })
  },

  // 新增软件登记信息
  createSoftwareInfo: async (data: SoftwareInfoVO) => {
    return await request.post({ url: `/software/registration-info/create`, data })
  },

  // 修改软件登记信息
  updateSoftwareInfo: async (data: SoftwareInfoVO) => {
    return await request.put({ url: `/software/registration-info/update`, data })
  },

  // 删除软件登记信息
  deleteSoftwareInfo: async (id: number) => {
    return await request.delete({ url: `/software/registration-info/delete?id=` + id })
  },

  // 导出软件登记信息 Excel
  exportSoftwareInfo: async (params) => {
    return await request.download({ url: `/software/registration-info/export-excel`, params })
  },
}
