import { 
  AppBusinessTypeEnum, 
  AppBusinessStatusEnum, 
  AppOrderStatusEnum,
  AppBusinessTypeConfig,
  AppOrderStatusConfig,
  AppBusinessStatusConfig
} from '@/types/business/businesscfg/app-business'

// ==================== 业务类型配置 ====================

export const APP_BUSINESS_TYPE_CONFIG: Record<AppBusinessTypeEnum, AppBusinessTypeConfig> = {
  [AppBusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE]: {
    code: 'data_certificate_evidence',
    name: '数据存证',
    description: '为数据提供时间戳和完整性证明，确保数据的法律效力',
    icon: 'document-copy',
    color: '#409EFF',
    features: ['数据完整性验证', '时间戳证明', '法律效力保障', '区块链存储'],
    defaultProcessingDays: 1
  },
  [AppBusinessTypeEnum.SOFTWARE_REGISTER]: {
    code: 'software_register',
    name: '软件著作权登记',
    description: '软件著作权登记申请和审核，保护软件知识产权',
    icon: 'cpu',
    color: '#67C23A',
    features: ['著作权保护', '法律认可', '知识产权证明', '专业审核'],
    defaultProcessingDays: 30
  },
  [AppBusinessTypeEnum.REAL_IDENTIFY]: {
    code: 'real_identify',
    name: '实名认证',
    description: '用户身份实名认证服务，提供可信身份验证',
    icon: 'user',
    color: '#E6A23C',
    features: ['身份验证', '实名保障', '安全认证', '快速审核'],
    defaultProcessingDays: 1
  },
  [AppBusinessTypeEnum.COPYRIGHT_REGISTER]: {
    code: 'copyright_register',
    name: '版权登记',
    description: '作品版权登记申请和审核，保护原创作品权益',
    icon: 'copyright',
    color: '#F56C6C',
    features: ['版权保护', '原创证明', '法律保障', '权益维护'],
    defaultProcessingDays: 15
  },
  [AppBusinessTypeEnum.BLOCKCHAIN_EVIDENCE]: {
    code: 'blockchain_evidence',
    name: '区块链存证',
    description: '基于区块链技术的数据存证服务，不可篡改永久保存',
    icon: 'link',
    color: '#909399',
    features: ['不可篡改', '去中心化', '永久保存', '技术先进'],
    defaultProcessingDays: 1
  }
}

// ==================== 订单状态配置 ====================

export const APP_ORDER_STATUS_CONFIG: Record<AppOrderStatusEnum, AppOrderStatusConfig> = {
  [AppOrderStatusEnum.UNPAID]: {
    status: 0,
    name: '待支付',
    color: 'warning',
    icon: 'clock',
    description: '订单已创建，等待支付'
  },
  [AppOrderStatusEnum.PAID]: {
    status: 10,
    name: '已支付',
    color: 'success',
    icon: 'check',
    description: '订单已支付，等待处理'
  },
  [AppOrderStatusEnum.DELIVERED]: {
    status: 20,
    name: '处理中',
    color: 'primary',
    icon: 'loading',
    description: '订单正在处理中'
  },
  [AppOrderStatusEnum.RECEIVED]: {
    status: 30,
    name: '待确认',
    color: 'info',
    icon: 'view',
    description: '处理完成，等待确认'
  },
  [AppOrderStatusEnum.COMPLETED]: {
    status: 40,
    name: '已完成',
    color: 'success',
    icon: 'circle-check',
    description: '订单已完成'
  },
  [AppOrderStatusEnum.CANCELLED]: {
    status: 50,
    name: '已取消',
    color: 'info',
    icon: 'circle-close',
    description: '订单已取消'
  }
}

// ==================== 业务状态配置 ====================

export const APP_BUSINESS_STATUS_CONFIG: Record<AppBusinessStatusEnum, AppBusinessStatusConfig> = {
  [AppBusinessStatusEnum.PENDING]: {
    status: 'PENDING',
    name: '待处理',
    color: 'warning',
    icon: 'clock',
    description: '业务申请已提交，等待处理'
  },
  [AppBusinessStatusEnum.PROCESSING]: {
    status: 'PROCESSING',
    name: '处理中',
    color: 'primary',
    icon: 'loading',
    description: '业务正在处理中'
  },
  [AppBusinessStatusEnum.COMPLETED]: {
    status: 'COMPLETED',
    name: '已完成',
    color: 'success',
    icon: 'circle-check',
    description: '业务处理完成'
  },
  [AppBusinessStatusEnum.FAILED]: {
    status: 'FAILED',
    name: '处理失败',
    color: 'danger',
    icon: 'circle-close',
    description: '业务处理失败'
  },
  [AppBusinessStatusEnum.CANCELLED]: {
    status: 'CANCELLED',
    name: '已取消',
    color: 'info',
    icon: 'remove',
    description: '业务已取消'
  }
}

// ==================== 会员等级配置 ====================

export const APP_MEMBER_LEVEL_CONFIG = {
  bronze: {
    name: '青铜会员',
    color: '#CD7F32',
    icon: 'medal',
    discountRate: 0.9, // 9折
    benefits: ['9折优惠', '专属客服', '优先处理']
  },
  silver: {
    name: '白银会员',
    color: '#C0C0C0',
    icon: 'medal',
    discountRate: 0.8, // 8折
    benefits: ['8折优惠', '专属客服', '优先处理', '免费咨询']
  },
  gold: {
    name: '黄金会员',
    color: '#FFD700',
    icon: 'medal',
    discountRate: 0.7, // 7折
    benefits: ['7折优惠', '专属客服', '优先处理', '免费咨询', '专属通道']
  }
}

// ==================== 界面配置 ====================

export const APP_UI_CONFIG = {
  // 分页配置
  pagination: {
    pageSize: 10,
    pageSizes: [10, 20, 50],
    layout: 'prev, pager, next, jumper, total'
  },
  
  // 图片配置
  image: {
    defaultAvatar: '/images/default-avatar.png',
    defaultBusinessIcon: '/images/default-business.png',
    placeholder: '/images/placeholder.png'
  },
  
  // 动画配置
  animation: {
    duration: 300,
    easing: 'ease-in-out'
  },
  
  // 颜色主题
  colors: {
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399'
  }
}

// ==================== 业务规则配置 ====================

export const APP_BUSINESS_RULES = {
  // 价格相关
  price: {
    minAmount: 1, // 最小金额（分）
    maxAmount: 999999999, // 最大金额（分）
    currency: 'CNY',
    currencySymbol: '¥'
  },
  
  // 订单相关
  order: {
    maxItemCount: 99, // 单个商品最大数量
    maxOrderItems: 20, // 订单最大商品种类
    cancelTimeLimit: 30 * 60 * 1000, // 取消时间限制（30分钟）
    payTimeLimit: 24 * 60 * 60 * 1000 // 支付时间限制（24小时）
  },
  
  // 文件上传
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
    maxFileCount: 10
  }
}

// ==================== 错误消息配置 ====================

export const APP_ERROR_MESSAGES = {
  network: '网络连接失败，请检查网络设置',
  timeout: '请求超时，请稍后重试',
  unauthorized: '登录已过期，请重新登录',
  forbidden: '没有权限访问该资源',
  notFound: '请求的资源不存在',
  serverError: '服务器内部错误，请稍后重试',
  
  // 业务相关错误
  business: {
    configNotFound: '业务配置不存在',
    orderNotFound: '订单不存在',
    paymentFailed: '支付失败，请重试',
    orderCancelled: '订单已取消',
    businessDataInvalid: '业务数据格式不正确'
  }
}

// ==================== 成功消息配置 ====================

export const APP_SUCCESS_MESSAGES = {
  orderCreated: '订单创建成功',
  paymentSuccess: '支付成功',
  orderCancelled: '订单取消成功',
  orderReceived: '确认收货成功',
  businessSubmitted: '业务申请提交成功',
  dataUploaded: '数据上传成功'
}

export default {
  APP_BUSINESS_TYPE_CONFIG,
  APP_ORDER_STATUS_CONFIG,
  APP_BUSINESS_STATUS_CONFIG,
  APP_MEMBER_LEVEL_CONFIG,
  APP_UI_CONFIG,
  APP_BUSINESS_RULES,
  APP_ERROR_MESSAGES,
  APP_SUCCESS_MESSAGES
}
