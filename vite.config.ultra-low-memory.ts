import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import UnoCSS from 'unocss/vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons-ng'

// 超低内存构建配置 - 专门为内存受限环境设计
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const root = process.cwd()

  // 路径查找
  function pathResolve(dir: string) {
    return resolve(root, '.', dir)
  }

  return {
    plugins: [
      vue({
        // 禁用模板预编译以减少内存使用
        template: {
          compilerOptions: {
            // 禁用优化以减少内存消耗
            hoistStatic: false,
            cacheHandlers: false
          }
        }
      }),
      UnoCSS(),
      createSvgIconsPlugin({
        iconDirs: [pathResolve('src/assets/svgs')],
        symbolId: 'icon-[dir]-[name]',
        svgoOptions: true
      })
    ],
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        'vue': 'vue/dist/vue.esm-bundler.js'
      }
    },
    
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    },
    
    build: {
      // 完全禁用压缩以节省内存
      minify: false,
      outDir: env.VITE_OUT_DIR || 'dist-prod',
      sourcemap: false,
      
      // 极小的chunk大小
      chunkSizeWarningLimit: 300,
      
      // 禁用所有优化以减少内存使用
      reportCompressedSize: false,
      cssCodeSplit: false,
      
      rollupOptions: {
        // 完全禁用并行处理
        maxParallelFileOps: 1,
        
        // 禁用缓存
        cache: false,
        
        // 极简的代码分割
        output: {
          manualChunks: (id) => {
            // 只做最基本的分割
            if (id.includes('node_modules')) {
              // 将所有第三方库打包到一个文件中
              return 'vendor'
            }
            // 其他所有代码打包到主文件中
            return 'main'
          },
          
          // 简单的文件命名
          chunkFileNames: 'js/[name].js',
          entryFileNames: 'js/[name].js',
          assetFileNames: 'assets/[name].[ext]'
        },
        
        // 外部化一些大型依赖（如果可能）
        external: (_id) => {
          // 可以考虑外部化一些大型库
          return false
        }
      }
    },
    
    // CSS预处理器配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
          javascriptEnabled: true
        }
      }
    },

    // 开发服务器配置（虽然这里用不到）
    server: {
      host: '0.0.0.0',
      port: 3000
    },
    
    // 优化依赖处理
    optimizeDeps: {
      // 禁用依赖预构建以减少内存使用
      noDiscovery: true,
      include: []
    }
  }
})
