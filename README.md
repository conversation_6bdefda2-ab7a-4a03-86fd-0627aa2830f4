# 文创链服务平台 2.0 - 管理系统前端

<div align="center">

![Logo](public/logo.gif)

**基于 Vue3 + TypeScript + Element Plus 的现代化企业级管理平台**

[![Vue](https://img.shields.io/badge/Vue-3.5.12-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3.3-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.9.1-409EFF?style=flat-square&logo=element)](https://element-plus.org/)
[![Vite](https://img.shields.io/badge/Vite-5.1.4-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![PNPM](https://img.shields.io/badge/PNPM-8.6.0+-F69220?style=flat-square&logo=pnpm)](https://pnpm.io/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

</div>

## 📋 目录

- [项目简介](#-项目简介)
- [核心特性](#-核心特性)
- [技术栈](#️-技术栈)
- [环境要求](#-环境要求)
- [快速开始](#-快速开始)
- [项目结构](#-项目结构)
- [环境配置](#-环境配置)
- [开发指南](#-开发指南)
- [构建部署](#-构建部署)
- [功能模块](#-功能模块)
- [上游同步](#-上游同步)
- [常见问题](#-常见问题)
- [贡献指南](#-贡献指南)
- [许可证](#-许可证)

## 📖 项目简介

文创链服务平台 2.0 是一个功能完整的企业级管理系统前端项目，专注于**软件著作权登记、实名认证、证书管理**等文创产业核心业务。项目基于最新的前端技术栈构建，提供了完整的业务功能模块和基础设施支持，具有高度的可扩展性和可维护性。

本项目基于芋道源码（yudao-ui-admin-vue3）进行定制开发，在保持上游框架优势的同时，深度集成了文创链特色业务功能，包括：

- 🏢 **实名认证管理** - 支持个人和企业实名认证流程
- 💻 **软件著作权登记** - 完整的软件登记申请和管理流程
- 📜 **证书信息管理** - 证书申请、审核、颁发全流程管理
- 🔄 **业务配置管理** - 灵活的业务参数和流程配置
- 📊 **数据统计分析** - 丰富的业务数据可视化展示

### ✨ 核心特性

#### 🏗️ 技术特性
- 🚀 **最新技术栈**：基于 Vue 3.5、Vite 5、TypeScript 5.3 等前沿技术
- 🎨 **现代化 UI**：使用 Element Plus 2.9 组件库，界面美观易用
- 🔐 **完善权限**：内置完整的 RBAC 权限管理系统
- 🌍 **国际化支持**：内置 i18n 国际化解决方案
- 📱 **响应式设计**：支持多终端适配，移动端友好
- 🎯 **TypeScript**：全面的类型支持，提升开发体验
- 🔧 **工程化**：完善的代码规范、自动化构建和部署流程
- 📊 **数据可视化**：集成 ECharts 图表库
- 🎪 **丰富组件**：提供大量业务组件和工具函数
- 🔄 **上游同步**：支持与芋道源码的定期同步更新

#### 💼 业务特性
- 🏢 **多类型实名认证**：支持个人、企业、事业单位等多种认证类型
- 💻 **软件著作权管理**：完整的软件登记、审核、证书颁发流程
- 📜 **证书全生命周期**：从申请到颁发的完整证书管理体系
- 🔄 **灵活业务配置**：支持业务参数、流程规则的动态配置
- 📈 **智能数据分析**：提供业务数据统计和趋势分析
- 🔍 **高级搜索过滤**：支持多维度条件筛选和数据导出
- 📋 **表单动态生成**：基于配置的动态表单生成和验证

### 🏗️ 系统架构

```
文创链服务平台 2.0
├── 🏢 业务管理          # 文创链核心业务模块
│   ├── 实名认证管理      # 个人/企业实名认证流程
│   ├── 软件著作权登记    # 软件登记申请和管理
│   ├── 证书信息管理      # 证书申请、审核、颁发
│   └── 业务配置管理      # 业务参数和流程配置
├── 👥 系统管理          # 用户、角色、菜单、部门等基础管理
├── 🔄 工作流程          # BPM 工作流引擎，支持复杂业务流程
├── 🤖 AI 智能          # AI 聊天、图像生成、知识库等
├── 🏪 CRM 系统         # 客户关系管理
├── 📦 ERP 系统         # 企业资源规划
├── 🛒 商城系统          # 电商平台功能
├── 🌐 物联网管理        # IoT 设备和产品管理
├── 💳 支付系统          # 多渠道支付集成
├── 👤 会员中心          # 会员管理和积分系统
├── 📱 微信公众号        # 微信生态集成
├── 📊 报表系统          # GoView 可视化报表
└── 🛠️ 基础设施          # 代码生成、文件管理、监控等
```

## 🛠️ 技术栈

### 核心框架

| 技术 | 版本 | 说明 |
|------|------|------|
| [Vue.js](https://vuejs.org/) | 3.5.12 | 渐进式 JavaScript 框架 |
| [TypeScript](https://www.typescriptlang.org/) | 5.3.3 | JavaScript 的超集，提供静态类型检查 |
| [Vite](https://vitejs.dev/) | 5.1.4 | 下一代前端构建工具 |
| [Vue Router](https://router.vuejs.org/) | 4.4.5 | Vue.js 官方路由管理器 |
| [Pinia](https://pinia.vuejs.org/) | 2.1.7 | Vue 的状态管理库 |

### UI 组件库

| 技术 | 版本 | 说明 |
|------|------|------|
| [Element Plus](https://element-plus.org/) | 2.9.1 | 基于 Vue 3 的组件库 |
| [UnoCSS](https://unocss.dev/) | 0.58.5 | 即时原子化 CSS 引擎 |
| [@element-plus/icons-vue](https://github.com/element-plus/element-plus-icons) | 2.1.0 | Element Plus 图标库 |
| [Animate.css](https://animate.style/) | 4.1.1 | CSS 动画库 |

### 开发工具

| 技术 | 版本 | 说明 |
|------|------|------|
| [ESLint](https://eslint.org/) | 8.57.0 | JavaScript 代码检查工具 |
| [Prettier](https://prettier.io/) | 3.2.5 | 代码格式化工具 |
| [Stylelint](https://stylelint.io/) | 16.2.1 | CSS 代码检查工具 |
| [Husky](https://typicode.github.io/husky/) | 8.0.3 | Git hooks 工具 |
| [Commitizen](https://github.com/commitizen/cz-cli) | 4.3.0 | 规范化 Git 提交信息 |

### 功能库

| 技术 | 版本 | 说明 |
|------|------|------|
| [Axios](https://axios-http.com/) | 1.6.8 | HTTP 客户端 |
| [ECharts](https://echarts.apache.org/) | 5.5.0 | 数据可视化图表库 |
| [Day.js](https://day.js.org/) | 1.11.10 | 轻量级日期处理库 |
| [Lodash-ES](https://lodash.com/) | 4.17.21 | JavaScript 实用工具库 |
| [Vue I18n](https://vue-i18n.intlify.dev/) | 9.10.2 | Vue.js 国际化插件 |
| [BPMN.js](https://bpmn.io/) | 17.9.2 | 工作流程设计器 |
| [WangEditor](https://www.wangeditor.com/) | 5.1.23 | 富文本编辑器 |

## 📋 环境要求

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Node.js**: >= 16.0.0 (推荐使用 LTS 版本)
- **包管理器**: pnpm >= 8.6.0 (必须使用 pnpm)

### 开发环境

```bash
# 检查 Node.js 版本
node --version  # 应该 >= 16.0.0

# 检查 pnpm 版本
pnpm --version  # 应该 >= 8.6.0

# 如果没有安装 pnpm
npm install -g pnpm
```

### 推荐开发工具

- **IDE**: [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)
- **浏览器**: Chrome 90+ / Firefox 88+ / Safari 14+
- **Git**: 2.20+

### VS Code 推荐插件

```json
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "stylelint.vscode-stylelint",
    "dbaeumer.vscode-eslint",
    "antfu.unocss"
  ]
}
```

| 技术                                                                   | 版本      | 说明                    |
|----------------------------------------------------------------------|---------|----------------------|
| [Vue 3](https://vuejs.org/)                                         | 3.5.12  | 渐进式 JavaScript 框架    |
| [TypeScript](https://www.typescriptlang.org/)                       | 5.3.3   | JavaScript 的超集       |
| [Vite](https://vitejs.dev/)                                         | 5.1.4   | 下一代前端构建工具            |
| [Element Plus](https://element-plus.org/)                           | 2.9.1   | Vue 3 组件库            |

### 状态管理与路由

| 技术                                                | 版本      | 说明              |
|---------------------------------------------------|---------|-----------------|
| [Pinia](https://pinia.vuejs.org/)                | 2.1.7   | Vue 状态管理库       |
| [Vue Router](https://router.vuejs.org/)          | 4.4.5   | Vue 官方路由管理器     |
| [Vue I18n](https://vue-i18n.intlify.dev/)        | 9.10.2  | Vue 国际化解决方案     |

### 工具库与插件

| 技术                                                | 版本      | 说明              |
|---------------------------------------------------|---------|-----------------|
| [VueUse](https://vueuse.org/)                     | 10.9.0  | Vue 组合式工具集      |
| [UnoCSS](https://unocss.dev/)                     | 0.58.5  | 即时原子化 CSS 引擎   |
| [Iconify](https://iconify.design/)                | 3.1.1   | 统一的图标框架         |
| [ECharts](https://echarts.apache.org/)            | 5.5.0   | 数据可视化图表库        |
| [Axios](https://axios-http.com/)                  | 1.6.8   | HTTP 客户端        |
| [Day.js](https://dayjs.gitee.io/)                 | 1.11.10 | 轻量级日期处理库        |
| [Lodash-ES](https://lodash.com/)                  | 4.17.21 | JavaScript 工具库   |

### 业务组件

| 技术                                                | 版本      | 说明              |
|---------------------------------------------------|---------|-----------------|
| [WangEditor](https://www.wangeditor.com/)         | 5.1.23  | 富文本编辑器          |
| [BPMN.js](https://bpmn.io/)                       | 17.9.2  | BPMN 流程设计器      |
| [Video.js](https://videojs.com/)                  | 7.21.5  | HTML5 视频播放器     |
| [CropperJS](https://fengyuanchen.github.io/cropperjs/) | 1.6.1   | 图片裁剪工具          |
| [QRCode](https://github.com/soldair/node-qrcode)  | 1.5.3   | 二维码生成工具         |
| [Vue3-Signature](https://github.com/WangJunZzz/vue3-signature) | 0.2.4   | 电子签名组件          |
| [VueDraggable](https://github.com/SortableJS/vue.draggable.next) | 4.1.0   | 拖拽排序组件          |
| [V3-JsonEditor](https://github.com/cloydlau/v3-jsoneditor) | 0.0.6   | JSON 编辑器        |

### 开发工具

| 技术                                                | 版本      | 说明              |
|---------------------------------------------------|---------|-----------------|
| [ESLint](https://eslint.org/)                     | 8.57.0  | JavaScript 代码检查工具 |
| [Prettier](https://prettier.io/)                  | 3.2.5   | 代码格式化工具         |
| [Stylelint](https://stylelint.io/)                | 16.2.1  | CSS 代码检查工具      |
| [Sass](https://sass-lang.com/)                    | 1.69.5  | CSS 预处理器        |

## 🚀 快速开始

### 获取项目代码

```bash
# 克隆项目
git clone https://github.com/your-repo/wenchuang-chain-manage-html-2.0.git

# 进入项目目录
cd wenchuang-chain-manage-html-2.0

# 安装依赖（必须使用 pnpm）
pnpm install

# 初始化 Git Hooks（首次克隆后执行）
pnpm prepare
```

### 开发调试

#### 基础启动命令

```bash
# 启动本地开发服务器（推荐）
pnpm dev

# 启动开发服务器（连接远程后端）
pnpm dev-server

# TypeScript 类型检查
pnpm ts:check
```

#### VSCode 调试配置

项目已配置完整的 VSCode 调试环境，支持断点调试和热重载：

**调试配置选项：**
- `调试：本地开发环境 (3000端口)` - 使用 Edge 浏览器调试本地环境
- `调试：本地开发环境 - Chrome (3000端口)` - 使用 Chrome 浏览器调试本地环境
- `调试：开发服务器环境 (3000端口)` - 调试连接远程后端的开发环境
- `调试：生产环境 (80端口)` - 调试生产环境构建
- `启动并调试：本地开发环境` - 自动启动服务器并开始调试
- `启动并调试：开发服务器环境` - 自动启动开发服务器并开始调试

**使用步骤：**
1. 在 VSCode 中按 `F5` 或点击"运行和调试"
2. 选择对应的调试配置
3. 设置断点并开始调试

**快速调试助手：**
```bash
# 使用调试助手快速启动
node scripts/debug-helper.js local    # 本地开发环境
node scripts/debug-helper.js dev      # 开发服务器环境
node scripts/debug-helper.js prod     # 生产环境

# 查看帮助
node scripts/debug-helper.js --help

# 等待服务器启动
node scripts/wait-for-server.js 3000
```

**调试故障排除：**
如果遇到调试问题，请参考 [调试配置指南](docs/DEBUG_GUIDE.md)，包含常见问题的解决方案。

### 构建部署

```bash
# 构建本地环境
pnpm build:local

# 构建开发环境
pnpm build:dev

# 构建测试环境
pnpm build:test

# 构建预发布环境
pnpm build:stage

# 构建生产环境
pnpm build:prod

# 构建并打包发布版本（包含压缩）
pnpm build:release

# Jenkins 构建（内存优化）
pnpm build:jenkins
pnpm build:jenkins:low-memory
pnpm build:jenkins:ultra-low-memory

# 预览构建结果
pnpm preview

# 预览开发环境构建
pnpm serve:dev

# 预览生产环境构建
pnpm serve:prod
```

### 代码规范

```bash
# ESLint 检查和修复
pnpm lint:eslint

# Prettier 格式化
pnpm lint:format

# Stylelint 检查和修复
pnpm lint:style

# 运行所有检查
pnpm lint:lint-staged

# 交互式提交（推荐）
pnpm commit
```

### Git Hooks 配置

项目使用 [Husky](https://typicode.github.io/husky/) 管理 Git hooks，确保代码质量：

```bash
# 初始化 Husky（首次克隆后运行）
pnpm prepare

# 或使用安装脚本
# Linux/macOS
chmod +x scripts/setup-husky.sh && ./scripts/setup-husky.sh

# Windows
scripts/setup-husky.bat
```

**已配置的 Git Hooks：**

- **pre-commit**: 运行 lint-staged，检查暂存文件的代码规范
- **commit-msg**: 验证提交信息格式是否符合约定式提交规范
- **pre-push**: 运行完整的代码检查和类型检查

## 📁 项目结构

```text
wenchuang-chain-manage-html-2.0/
├── build/                    # 构建配置
│   └── vite/                # Vite 相关配置
│       ├── index.ts         # 插件配置
│       └── optimize.ts      # 依赖优化配置
├── docs/                    # 项目文档
│   ├── COMMIT_EXAMPLES.md   # 提交信息示例
│   ├── HUSKY.md            # Husky 配置说明
│   ├── SYNC_IMPLEMENTATION_GUIDE.md  # 同步实现指南
│   └── UPSTREAM_SYNC_GUIDE.md        # 上游同步指南
├── public/                  # 静态资源
│   ├── favicon.ico         # 网站图标
│   ├── logo.gif           # 项目 Logo
│   └── home.png           # 首页图片
├── scripts/                 # 脚本文件
│   ├── setup-husky.sh      # Husky 安装脚本（Linux/macOS）
│   ├── setup-husky.bat     # Husky 安装脚本（Windows）
│   ├── sync-modules.js     # 模块同步脚本
│   └── zip-dist.js         # 打包压缩脚本
├── src/                     # 源代码
│   ├── api/                 # API 接口
│   │   ├── business/        # 🏢 业务相关接口（文创链核心）
│   │   │   ├── businesscfg/ # 业务配置管理
│   │   │   ├── certificateinfo/ # 证书信息管理
│   │   │   ├── certificatereview/ # 证书审核管理
│   │   │   ├── identify/    # 实名认证管理
│   │   │   └── software/    # 软件著作权管理
│   │   ├── system/         # 系统管理接口
│   │   ├── ai/             # AI 相关接口
│   │   ├── bpm/            # 工作流接口
│   │   ├── crm/            # CRM 接口
│   │   ├── erp/            # ERP 接口
│   │   ├── infra/          # 基础设施接口
│   │   ├── iot/            # 物联网接口
│   │   ├── mall/           # 商城接口
│   │   ├── member/         # 会员接口
│   │   ├── mp/             # 微信公众号接口
│   │   └── pay/            # 支付接口
│   ├── assets/             # 静态资源
│   │   ├── ai/            # AI 相关资源
│   │   ├── imgs/          # 图片资源
│   │   ├── svgs/          # SVG 图标
│   │   └── audio/         # 音频资源
│   ├── components/         # 公共组件
│   │   ├── Form/          # 表单组件
│   │   ├── Table/         # 表格组件
│   │   ├── Dialog/        # 对话框组件
│   │   ├── Editor/        # 编辑器组件
│   │   ├── Echart/        # 图表组件
│   │   └── ...            # 其他业务组件
│   ├── config/             # 配置文件
│   │   └── axios/         # HTTP 请求配置
│   ├── directives/         # 自定义指令
│   ├── hooks/              # 组合式函数
│   │   ├── web/           # Web 相关 hooks
│   │   └── event/         # 事件相关 hooks
│   ├── layout/             # 布局组件
│   ├── locales/            # 国际化文件
│   │   ├── zh-CN.ts       # 中文语言包
│   │   └── en.ts          # 英文语言包
│   ├── plugins/            # 插件配置
│   │   ├── elementPlus/   # Element Plus 配置
│   │   ├── vueI18n/       # 国际化配置
│   │   └── echarts/       # 图表配置
│   ├── router/             # 路由配置
│   │   ├── index.ts       # 路由主文件
│   │   └── modules/       # 路由模块
│   ├── store/              # 状态管理
│   │   ├── index.ts       # Store 主文件
│   │   └── modules/       # Store 模块
│   ├── styles/             # 样式文件
│   │   ├── index.scss     # 主样式文件
│   │   ├── variables.scss # 样式变量
│   │   └── theme.scss     # 主题样式
│   ├── types/              # TypeScript 类型定义
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   │   ├── business/       # 🏢 业务管理页面（文创链核心）
│   │   │   ├── businesscfg/ # 业务配置管理页面
│   │   │   ├── certificateinfo/ # 证书信息管理页面
│   │   │   ├── certificatereview/ # 证书审核管理页面
│   │   │   ├── identify/    # 实名认证管理页面
│   │   │   └── software/    # 软件著作权管理页面
│   │   ├── system/        # 系统管理页面
│   │   ├── ai/            # AI 功能页面
│   │   ├── bpm/           # 工作流页面
│   │   ├── crm/           # CRM 页面
│   │   ├── erp/           # ERP 页面
│   │   ├── mall/          # 商城页面
│   │   ├── iot/           # 物联网页面
│   │   ├── pay/           # 支付页面
│   │   ├── member/        # 会员页面
│   │   ├── mp/            # 微信公众号页面
│   │   └── report/        # 报表系统页面
│   ├── App.vue             # 根组件
│   ├── main.ts             # 应用入口
│   └── permission.ts       # 权限控制
├── types/                   # 全局类型定义
├── .env                     # 环境变量（基础配置）
├── .env.local              # 本地开发环境变量
├── .env.dev                # 开发环境变量
├── .env.test               # 测试环境变量
├── .env.stage              # 预发布环境变量
├── .env.prod               # 生产环境变量
├── vite.config.ts          # Vite 配置
├── tsconfig.json           # TypeScript 配置
├── package.json            # 项目依赖配置
├── pnpm-lock.yaml          # 依赖锁定文件
├── uno.config.ts           # UnoCSS 配置
├── sync.config.js          # 上游同步配置
└── README.md               # 项目说明文档
```

## ⚙️ 环境配置

项目支持多环境配置，通过不同的环境文件来管理各环境的配置参数。

### 环境文件说明

| 文件 | 环境 | 用途 | 端口 |
|------|------|------|------|
| `.env` | 基础配置 | 所有环境的默认配置 | 80 |
| `.env.local` | 本地开发 | 本地开发环境，连接本地后端 | 3000 |
| `.env.dev` | 开发环境 | 连接开发服务器 | 3000 |
| `.env.test` | 测试环境 | 测试环境构建 | 3000 |
| `.env.stage` | 预发布环境 | 预发布环境构建 | 3000 |
| `.env.prod` | 生产环境 | 生产环境构建 | 80 |

### 主要配置项

```bash
# 应用标题
VITE_APP_TITLE=文创链2.0

# 运行端口
VITE_PORT=3000

# 是否自动打开浏览器
VITE_OPEN=true

# 后端 API 地址
VITE_BASE_URL=http://localhost:48080

# API 路径前缀
VITE_API_URL=/admin-api

# 文件上传方式 (server/client)
VITE_UPLOAD_TYPE=server

# 是否启用验证码
VITE_APP_CAPTCHA_ENABLE=true

# 是否启用租户功能
VITE_APP_TENANT_ENABLE=true

# 构建时是否删除 console.log
VITE_DROP_CONSOLE=false

# 构建时是否删除 debugger
VITE_DROP_DEBUGGER=false

# 是否生成 sourcemap
VITE_SOURCEMAP=false

# 构建输出目录
VITE_OUT_DIR=dist

# 部署基础路径
VITE_BASE_PATH=/
```

### 端口配置策略

为了避免开发时的端口冲突问题，项目采用以下端口策略：

- **生产环境**: 使用标准的 80 端口
- **开发/测试环境**: 使用 3000 端口，避免权限问题

### 调试配置优化

项目已优化调试配置，确保开发和调试环境端口一致：

- **开发启动**: `pnpm dev` 使用 3000 端口
- **调试配置**: VSCode 调试器自动连接到 3000 端口
- **生产预览**: `pnpm preview` 使用 80 端口（需要管理员权限）

**调试端口映射：**
- 本地开发环境：`http://localhost:3000`
- 开发服务器环境：`http://localhost:3000`
- 生产环境：`http://localhost:80`

### 环境切换

```bash
# 本地开发（推荐）
pnpm dev                    # 使用 .env.local

# 连接开发服务器
pnpm dev-server            # 使用 .env.dev

# 构建不同环境
pnpm build:local           # 使用 .env + .env.local
pnpm build:dev             # 使用 .env.dev
pnpm build:test            # 使用 .env.test
pnpm build:stage           # 使用 .env.stage
pnpm build:prod            # 使用 .env.prod
```

## 🛠️ 开发指南

### 代码规范

项目使用 ESLint + Prettier + Stylelint 来保证代码质量和风格统一。

#### ESLint 配置

- 基于 `@typescript-eslint/recommended` 规则
- 集成 Vue 3 相关规则
- 支持 TypeScript 语法检查

#### Prettier 配置

```json
{
  "semi": false,
  "singleQuote": true,
  "printWidth": 100,
  "trailingComma": "none",
  "endOfLine": "auto"
}
```

#### Stylelint 配置

- 基于 `stylelint-config-standard` 规则
- 支持 SCSS 语法检查
- 集成 CSS 属性排序

### 提交规范

项目使用 [约定式提交](https://www.conventionalcommits.org/zh-hans/) 规范：

```bash
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交类型

| 类型 | 说明 |
|------|------|
| `feat` | 新功能 |
| `fix` | 修复 bug |
| `docs` | 文档更新 |
| `style` | 代码格式调整（不影响功能） |
| `refactor` | 代码重构 |
| `perf` | 性能优化 |
| `test` | 测试相关 |
| `chore` | 构建过程或辅助工具的变动 |
| `ci` | CI/CD 相关 |
| `build` | 构建系统或外部依赖的变动 |

#### 提交示例

```bash
# 使用 commitizen 交互式提交（推荐）
pnpm commit

# 手动提交示例
git commit -m "feat(user): 添加用户管理功能"
git commit -m "fix(login): 修复登录页面验证码显示问题"
git commit -m "docs(readme): 更新项目文档"
```

### 组件开发

#### 组件命名规范

- **页面组件**: 使用 PascalCase，如 `UserList.vue`
- **公共组件**: 使用 PascalCase，如 `BaseTable.vue`
- **组件文件夹**: 使用 PascalCase，如 `UserManagement/`

#### 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, reactive } from 'vue'

// 定义 props
interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '默认标题'
})

// 定义 emits
const emit = defineEmits<{
  change: [value: string]
}>()

// 组件逻辑
const state = reactive({
  loading: false
})
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

### API 接口

#### 接口文件组织

```text
src/api/
├── system/          # 系统管理相关接口
│   ├── user.ts     # 用户管理
│   ├── role.ts     # 角色管理
│   └── menu.ts     # 菜单管理
├── business/         # 业务相关接口
└── common/          # 公共接口
```

#### 接口定义规范

```typescript
// src/api/system/user.ts
import request from '@/config/axios'

// 定义接口类型
export interface UserVO {
  id: number
  username: string
  nickname: string
  email: string
  mobile: string
  status: number
  createTime: Date
}

export interface UserPageReqVO extends PageParam {
  username?: string
  mobile?: string
  status?: number
}

// API 接口定义
export const getUserPage = (params: UserPageReqVO) => {
  return request.get({ url: '/system/user/page', params })
}

export const getUser = (id: number) => {
  return request.get({ url: `/system/user/get?id=${id}` })
}

export const createUser = (data: UserVO) => {
  return request.post({ url: '/system/user/create', data })
}

export const updateUser = (data: UserVO) => {
  return request.put({ url: '/system/user/update', data })
}

export const deleteUser = (id: number) => {
  return request.delete({ url: `/system/user/delete?id=${id}` })
}
```

## 🏢 功能模块详解

### 核心业务模块

#### 1. 实名认证管理 (`src/views/business/identify/`)

**功能概述**：提供个人和企业的实名认证服务，支持多种认证类型和审核流程。

**主要功能**：
- 📋 **多类型认证**：支持个人、企业、事业单位等多种认证类型
- 🔍 **智能审核**：基于规则引擎的自动审核和人工审核
- 📊 **认证统计**：认证数量、通过率等数据统计
- 📄 **材料管理**：支持多种证件材料上传和验证
- 🔄 **状态跟踪**：完整的认证流程状态跟踪

**技术特点**：
- 动态表单生成和验证
- 文件上传和预览功能
- 状态机管理认证流程

#### 2. 软件著作权登记 (`src/views/business/software/`)

**功能概述**：完整的软件著作权登记申请、审核、证书颁发管理系统。

**主要功能**：
- 💻 **登记申请**：支持原创、修改、合作开发等多种登记类型
- 📝 **材料管理**：源代码、说明书、申请表等材料管理
- 🔍 **审核流程**：多级审核和状态管理
- 📜 **证书管理**：电子证书生成和管理
- 📊 **统计分析**：登记数量、类型分布等统计

**技术特点**：
- 复杂表单设计和验证
- 大文件上传和处理
- 工作流引擎集成

#### 3. 证书信息管理 (`src/views/business/certificateinfo/`)

**功能概述**：统一的证书信息管理平台，支持各类证书的申请、审核、颁发。

**主要功能**：
- 📜 **证书申请**：在线证书申请和材料提交
- 🔍 **审核管理**：多级审核和批量处理
- 📋 **证书颁发**：电子证书生成和发放
- 🔍 **证书查询**：证书真伪验证和查询服务
- 📊 **数据统计**：证书颁发统计和分析

#### 4. 业务配置管理 (`src/views/business/businesscfg/`)

**功能概述**：灵活的业务参数配置和流程管理系统。

**主要功能**：
- ⚙️ **参数配置**：业务规则和参数的动态配置
- 🔄 **流程配置**：审核流程和状态转换配置
- 💰 **价格矩阵**：服务价格和费用配置
- 🔗 **订单关联**：业务订单和服务的关联配置
- 📊 **配置统计**：配置使用情况统计

### 扩展功能模块

#### AI 智能模块 (`src/views/ai/`)
- 🤖 **AI 聊天**：智能客服和问答系统
- 🎨 **图像生成**：AI 图像创作和编辑
- 📚 **知识库**：智能知识管理和检索
- 🧠 **思维导图**：AI 辅助思维导图生成
- 🎵 **音乐生成**：AI 音乐创作功能
- ✍️ **智能写作**：AI 辅助文档写作

#### 工作流引擎 (`src/views/bpm/`)
- 📋 **流程设计**：可视化流程设计器
- 🔄 **流程实例**：流程执行和监控
- 📝 **表单设计**：动态表单设计器
- 👥 **用户组管理**：流程参与者管理
- 📊 **流程统计**：流程执行效率分析

## 🔄 上游同步

本项目基于芋道源码（yudao-ui-admin-vue3）进行定制开发，支持与上游仓库的定期同步更新。

### 同步策略

项目采用**选择性同步**策略，确保在获得上游更新的同时保护业务定制代码：

#### 🟢 安全同步模块（可直接同步）

- **基础组件**: `src/components/common/`
- **工具函数**: `src/utils/`
- **组合式函数**: `src/hooks/`
- **自定义指令**: `src/directives/`
- **公共样式**: `src/styles/common/`
- **类型定义**: `types/`
- **构建配置**: `build/`

#### 🟡 谨慎同步模块（需要手动检查）

- **系统管理**: `src/views/system/`、`src/api/system/`
- **基础设施**: `src/views/infra/`、`src/api/infra/`
- **依赖配置**: `package.json`
- **构建配置**: `vite.config.ts`

#### 🔴 保护模块（不同步，保留业务定制）

- **业务管理**: `src/views/business/`、`src/api/business/`
- **AI 模块**: `src/views/ai/`、`src/api/ai/`
- **IoT 模块**: `src/views/iot/`、`src/api/iot/`
- **主配置**: `src/main.ts`、`src/App.vue`
- **权限配置**: `src/permission.ts`
- **环境配置**: `.env*` 文件

### 同步操作

#### 1. 配置上游仓库

```bash
# 添加芋道源码作为上游仓库
git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git

# 验证远程仓库配置
git remote -v
```

#### 2. 获取上游更新

```bash
# 获取上游最新代码
git fetch upstream

# 查看上游更新日志
git log --oneline HEAD..upstream/master
```

#### 3. 执行同步

```bash
# 使用自动化同步脚本（推荐）
pnpm run sync:upstream

# 或手动同步特定模块
git checkout upstream/master -- src/components/common/
git checkout upstream/master -- src/utils/
```

#### 4. 使用同步配置

项目提供了 `sync.config.js` 配置文件来管理同步策略：

```javascript
// sync.config.js
module.exports = {
  // 上游仓库信息
  upstream: {
    url: 'https://gitee.com/yudaocode/yudao-ui-admin-vue3.git',
    branch: 'master'
  },

  // 安全同步的路径（可自动同步）
  safePaths: [
    'src/components/common/',
    'src/utils/',
    'src/hooks/',
    'src/directives/',
    'src/styles/common/',
    'types/',
    'build/'
  ],

  // 保护的路径（不同步）
  protectedPaths: [
    'src/views/business/',
    'src/api/business/',
    'src/views/ai/',
    'src/api/ai/',
    'src/views/iot/',
    'src/api/iot/',
    'src/main.ts',
    'src/App.vue',
    'src/permission.ts',
    '.env*'
  ],

  // 冲突解决策略
  conflictResolution: {
    default: 'ours',  // 默认保留我们的版本
    fileStrategies: {
      'package.json': 'manual',     // 手动处理
      'vite.config.ts': 'manual'    // 手动处理
    }
  }
}
```

### 同步最佳实践

#### 1. 定期同步检查

建议每月检查一次上游更新：

```bash
# 检查上游更新
git fetch upstream
git log --oneline HEAD..upstream/master --pretty=format:"%h %s"
```

#### 2. 创建同步分支

```bash
# 创建同步分支
git checkout -b sync/upstream-$(date +%Y%m%d)

# 执行同步操作
# ...

# 测试验证后合并
git checkout main
git merge sync/upstream-$(date +%Y%m%d)
```

#### 3. 同步后验证

```bash
# TypeScript 检查
pnpm ts:check

# 代码规范检查
pnpm lint:eslint

# 测试构建
pnpm build:test
```

### 同步文档

详细的同步操作指南请参考：

- [上游同步指南](docs/UPSTREAM_SYNC_GUIDE.md)
- [同步实现指南](docs/SYNC_IMPLEMENTATION_GUIDE.md)

## 🚀 构建部署

### 构建命令

```bash
# 构建生产环境
pnpm build:prod

# 构建并打包发布版本（包含压缩）
pnpm build:release

# Jenkins 构建（针对不同内存环境）
pnpm build:jenkins              # 标准 Jenkins 构建
pnpm build:jenkins:low-memory   # 低内存环境构建
pnpm build:jenkins:ultra-low-memory  # 超低内存环境构建
```

### Jenkins CI/CD 支持

项目提供完整的 Jenkins 构建支持，针对不同内存环境进行了优化：

#### Jenkinsfile 特性
- 🔍 **环境检查**：自动检查 Node.js、npm 版本和系统资源
- 🧹 **智能清理**：构建前后的缓存和临时文件清理
- 📦 **依赖优化**：使用 `npm ci` 提升安装速度
- 💾 **内存优化**：针对不同内存环境的构建参数调优
- 📁 **产物归档**：自动归档构建产物和指纹识别

#### 内存优化策略
```bash
# 标准环境（4GB+）
NODE_OPTIONS="--max_old_space_size=3584 --optimize-for-size"

# 低内存环境（2GB）
NODE_OPTIONS="--max_old_space_size=1536 --max-semi-space-size=64"

# 超低内存环境（1GB）
NODE_OPTIONS="--max_old_space_size=768 --max-semi-space-size=32"
```

### 部署配置

#### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/wenchuang-chain-2.0;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理（文创链后端）
    location /admin-api/ {
        proxy_pass http://backend-server:48080/admin-api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 支持大文件上传（软件著作权材料）
        client_max_body_size 100M;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;
}
```

#### Docker 部署

```dockerfile
# Dockerfile
FROM nginx:alpine

# 安装必要工具
RUN apk add --no-cache tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 复制构建产物
COPY dist-prod/ /usr/share/nginx/html/

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html
RUN chown -R nginx:nginx /var/log/nginx

EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

```bash
# 构建 Docker 镜像
docker build -t wenchuang-chain-frontend:latest .

# 运行容器
docker run -d \
  --name wenchuang-frontend \
  -p 80:80 \
  -v /var/log/nginx:/var/log/nginx \
  --restart unless-stopped \
  wenchuang-chain-frontend:latest

# 使用 Docker Compose
cat > docker-compose.yml << EOF
version: '3.8'
services:
  frontend:
    build: .
    ports:
      - "80:80"
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
EOF

docker-compose up -d
```

#### 环境变量配置

部署时需要根据实际环境配置相应的环境变量：

```bash
# 生产环境配置示例
VITE_BASE_URL=https://api.your-domain.com
VITE_API_URL=/admin-api
VITE_UPLOAD_TYPE=server
VITE_APP_CAPTCHA_ENABLE=true
VITE_APP_TENANT_ENABLE=true
```

## ❓ 常见问题

### 安装和启动问题

#### Q: 安装依赖时报错 "EACCES: permission denied"

**A**: 这通常是 npm 权限问题，建议使用 pnpm：

```bash
# 卸载全局 npm 包
npm uninstall -g npm

# 安装 pnpm
curl -fsSL https://get.pnpm.io/install.sh | sh

# 重新安装依赖
pnpm install
```

#### Q: 启动时端口被占用

**A**: 检查端口占用并修改配置：

```bash
# 检查端口占用
netstat -ano | findstr :3000

# 修改 .env.local 中的端口
VITE_PORT=3001
```

#### Q: 启动时卡住不动

**A**: 这通常是首次启动时 Vite 预构建依赖导致的，请耐心等待 1-2 分钟。如果长时间无响应：

```bash
# 清理缓存重新启动
pnpm clean:cache
pnpm install
pnpm dev
```

### 开发问题

#### Q: TypeScript 类型错误

**A**: 运行类型检查并修复：

```bash
# 检查类型错误
pnpm ts:check

# 重新生成类型文件
rm -rf src/types/auto-*.d.ts
pnpm dev
```

#### Q: ESLint 规则冲突

**A**: 统一代码风格：

```bash
# 自动修复 ESLint 错误
pnpm lint:eslint

# 格式化代码
pnpm lint:format
```

#### Q: 组件自动导入失败

**A**: 检查组件注册：

```bash
# 重新生成组件类型
rm src/types/auto-components.d.ts
pnpm dev
```

### 构建问题

#### Q: 构建时内存不足

**A**: 根据环境选择合适的构建命令：

```bash
# 标准环境（4GB+ 内存）
pnpm build:prod

# Jenkins 环境（2-4GB 内存）
pnpm build:jenkins

# 低内存环境（1-2GB 内存）
pnpm build:jenkins:low-memory

# 超低内存环境（<1GB 内存）
pnpm build:jenkins:ultra-low-memory

# 手动设置内存限制
export NODE_OPTIONS="--max_old_space_size=2048 --optimize-for-size"
pnpm build:prod
```

#### Q: 构建产物过大

**A**: 检查打包分析和优化：

```bash
# 分析构建产物
pnpm build:prod
# 查看 dist-prod 目录大小
du -sh dist-prod

# 检查大文件
find dist-prod -type f -size +1M -exec ls -lh {} \;

# 优化建议：
# 1. 检查是否有未使用的依赖
# 2. 确认图片资源是否已压缩
# 3. 检查是否启用了代码分割
```

#### Q: Jenkins 构建失败

**A**: 检查 Jenkins 环境配置：

```bash
# 检查 Node.js 版本
node --version  # 应该 >= 16.0.0

# 检查可用内存
free -h

# 清理 Jenkins 工作空间
rm -rf node_modules/.cache
rm -rf dist-prod
npm cache clean --force

# 使用适合的构建脚本
chmod +x scripts/jenkins-build.sh
./scripts/jenkins-build.sh
```

### 上游同步问题

#### Q: 同步时出现大量冲突

**A**: 使用选择性同步策略：

```bash
# 使用快速同步脚本
# Windows
scripts\quick-sync.bat

# Linux/macOS
chmod +x scripts/quick-sync.sh
./scripts/quick-sync.sh

# 手动选择性同步
git checkout upstream/master -- src/utils/
git checkout upstream/master -- src/hooks/
git checkout upstream/master -- src/components/Form/

# 跳过业务模块
git reset HEAD src/views/business/
git reset HEAD src/api/business/
```

#### Q: 同步后功能异常

**A**: 回滚并重新同步：

```bash
# 回滚到同步前状态
git reset --hard HEAD~1

# 使用更保守的同步策略
git checkout upstream/master -- src/components/common/

# 验证同步结果
pnpm ts:check
pnpm lint:eslint
pnpm build:test
```

#### Q: 如何查看上游更新内容

**A**: 查看上游变更日志：

```bash
# 获取上游更新
git fetch upstream

# 查看上游更新日志
git log --oneline HEAD..upstream/master

# 查看具体文件变更
git diff HEAD..upstream/master --name-only

# 查看特定模块的变更
git diff HEAD..upstream/master -- src/components/
```

## 🤝 贡献指南

### 参与贡献

我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ⚡ 性能优化

### 贡献流程

1. **Fork 项目**

```bash
# Fork 项目到你的 GitHub 账号
# 然后克隆到本地
git clone https://github.com/your-username/wenchuang-chain-manage-html-2.0.git
```

2. **创建功能分支**

```bash
# 创建并切换到新分支
git checkout -b feature/your-feature-name

# 或修复分支
git checkout -b fix/your-fix-name
```

3. **开发和测试**

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm ts:check
pnpm lint:eslint
```

4. **提交代码**

```bash
# 使用规范的提交信息
pnpm commit

# 或手动提交
git add .
git commit -m "feat: 添加新功能描述"
```

5. **推送并创建 PR**

```bash
# 推送到你的仓库
git push origin feature/your-feature-name

# 在 GitHub 上创建 Pull Request
```

### 代码审查

所有的代码变更都需要通过代码审查：

- ✅ 代码风格符合项目规范
- ✅ 通过所有自动化检查
- ✅ 包含必要的测试
- ✅ 更新相关文档

### 问题报告

报告 Bug 时请提供：

- 🔍 详细的问题描述
- 🔄 复现步骤
- 💻 运行环境信息
- 📸 截图或错误日志

- **pre-commit**: 代码检查和格式化
- **commit-msg**: 提交信息格式检查
- **pre-push**: 推送前完整检查

**提交信息规范：**

```bash
# 格式：<type>(<scope>): <subject>
feat(user): 添加用户管理功能
fix(login): 修复登录页面样式问题
docs(readme): 更新项目文档
```

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议，允许商业和非商业用途。

## 🙏 致谢

感谢以下开源项目的支持：

- [芋道源码](https://gitee.com/yudaocode/yudao-ui-admin-vue3) - 提供优秀的基础框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [TypeScript](https://www.typescriptlang.org/) - JavaScript 的超集
- [UnoCSS](https://unocss.dev/) - 即时原子化 CSS 引擎
- [BPMN.js](https://bpmn.io/) - 工作流程设计器
- [ECharts](https://echarts.apache.org/) - 数据可视化图表库

## 📊 项目统计

- 📁 **代码行数**: 50,000+ 行
- 🎨 **页面组件**: 200+ 个
- 🔧 **API 接口**: 500+ 个
- 📦 **依赖包**: 150+ 个
- 🌟 **功能模块**: 15+ 个
- 🔄 **持续更新**: 与上游框架保持同步

## 📞 联系我们

- **项目地址**：[GitHub Repository](https://github.com/your-repo/wenchuang-chain-manage-html-2.0)
- **问题反馈**：[Issues](https://github.com/your-repo/wenchuang-chain-manage-html-2.0/issues)
- **功能建议**：[Discussions](https://github.com/your-repo/wenchuang-chain-manage-html-2.0/discussions)
- **技术交流**：欢迎提交 PR 和 Issue
- **商务合作**：请通过 Issues 联系我们

## 📈 更新日志

### v2.6.0 (当前版本)
- ✨ 新增文创链核心业务模块
- 🔧 优化 Jenkins 构建流程
- 📊 增强数据统计和可视化功能
- 🐛 修复已知问题和性能优化
- 📝 完善项目文档和使用指南

### 未来规划
- 🚀 移动端适配优化
- 🤖 AI 功能深度集成
- 📊 更丰富的数据分析功能
- 🔐 安全性进一步增强
- 🌐 国际化支持完善

---

<div align="center">

**如果这个项目对你有帮助，请给个 ⭐ Star 支持一下！**

Made with ❤️ by 文创链团队

</div>
