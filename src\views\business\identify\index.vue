<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="主键" prop="identifyId">
        <el-input
          v-model="queryParams.identifyId"
          placeholder="请输入主键"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="认证类型" prop="identifyType">
        <el-select
          v-model="queryParams.identifyType"
          placeholder="请选择认证类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.IDENTIFY_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="企业类型" prop="enterpriseType">
        <el-select
          v-model="queryParams.enterpriseType"
          placeholder="请选择企业类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="证件类型" prop="idType">
        <el-select
          v-model="queryParams.idType"
          placeholder="请选择证件类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="真实名称" prop="idName">
        <el-input
          v-model="queryParams.idName"
          placeholder="请输入真实名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="法人名称" prop="legalName">
        <el-input
          v-model="queryParams.legalName"
          placeholder="请输入法人名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="证件号码" prop="idNo">
        <el-input
          v-model="queryParams.idNo"
          placeholder="请输入证件号码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="人像面身份证" prop="faceCard">
        <el-input
          v-model="queryParams.faceCard"
          placeholder="请输入人像面身份证"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="背面身份证" prop="backCard">
        <el-input
          v-model="queryParams.backCard"
          placeholder="请输入背面身份证"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="营业执照" prop="license">
        <el-input
          v-model="queryParams.license"
          placeholder="请输入营业执照"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-input
          v-model="queryParams.country"
          placeholder="请输入国家"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入省份"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入城市"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入详细地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="注册地" prop="registrationPlace">
        <el-input
          v-model="queryParams.registrationPlace"
          placeholder="请输入注册地"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="注册区域" prop="registrationArea">
        <el-input
          v-model="queryParams.registrationArea"
          placeholder="请输入注册区域"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="主营业务/业务范围" prop="businessScope">
        <el-input
          v-model="queryParams.businessScope"
          placeholder="请输入主营业务/业务范围"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="企业电话" prop="enterprisePhone">
        <el-input
          v-model="queryParams.enterprisePhone"
          placeholder="请输入企业电话"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="企业邮箱" prop="enterpriseEmail">
        <el-input
          v-model="queryParams.enterpriseEmail"
          placeholder="请输入企业邮箱"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="营业期限" prop="businessTerm">
        <el-input
          v-model="queryParams.businessTerm"
          placeholder="请输入营业期限"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="statusCd">
        <el-select
          v-model="queryParams.statusCd"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.GENERAL_STATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="状态时间" prop="statusDate">
        <el-date-picker
          v-model="queryParams.statusDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createStaff">
        <el-input
          v-model="queryParams.createStaff"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.createDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="文创链用户ID" prop="wcUserId">
        <el-input
          v-model="queryParams.wcUserId"
          placeholder="请输入文创链用户ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['business:identify:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['business:identify:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="认证类型" align="center" prop="identifyType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.IDENTIFY_TYPE" :value="scope.row.identifyType" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="企业类型" align="center" prop="enterpriseType" />
      <el-table-column label="证件类型" align="center" prop="idType" /> -->
      <el-table-column label="真实名称" align="center" prop="idName" />
      <!-- <el-table-column label="手机号" align="center" prop="phone" /> -->
      <el-table-column label="法人名称" align="center" prop="legalName" />
      <el-table-column label="证件号码" align="center" prop="idNo" />
      <!-- <el-table-column label="人像面身份证" align="center" prop="faceCard" />
      <el-table-column label="背面身份证" align="center" prop="backCard" />
      <el-table-column label="营业执照" align="center" prop="license" />
      <el-table-column label="国家" align="center" prop="country" />
      <el-table-column label="省份" align="center" prop="province" />
      <el-table-column label="城市" align="center" prop="city" />
      <el-table-column label="详细地址" align="center" prop="address" />
      <el-table-column label="注册地" align="center" prop="registrationPlace" />
      <el-table-column label="注册区域" align="center" prop="registrationArea" />
      <el-table-column label="主营业务/业务范围" align="center" prop="businessScope" />
      <el-table-column label="企业电话" align="center" prop="enterprisePhone" />
      <el-table-column label="企业邮箱" align="center" prop="enterpriseEmail" />
      <el-table-column label="营业期限" align="center" prop="businessTerm" /> -->
      <el-table-column label="状态" align="center" prop="statusCd">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.GENERAL_STATE" :value="scope.row.statusCd" />
        </template>
      </el-table-column>
      <el-table-column
        label="状态时间"
        align="center"
        prop="statusDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="创建人" align="center" prop="createStaff" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <!-- <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="文创链用户ID" align="center" prop="wcUserId" /> -->
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['business:identify:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['business:identify:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <IdentifyForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { IdentifyApi, IdentifyVO } from '@/api/business/identify'
import IdentifyForm from './IdentifyForm.vue'

/** 实名认证 列表 */
defineOptions({ name: 'Identify' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<IdentifyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  identifyId: undefined,
  userId: undefined,
  identifyType: undefined,
  enterpriseType: undefined,
  idType: undefined,
  idName: undefined,
  phone: undefined,
  legalName: undefined,
  idNo: undefined,
  faceCard: undefined,
  backCard: undefined,
  license: undefined,
  country: undefined,
  province: undefined,
  city: undefined,
  address: undefined,
  registrationPlace: undefined,
  registrationArea: undefined,
  businessScope: undefined,
  enterprisePhone: undefined,
  enterpriseEmail: undefined,
  businessTerm: undefined,
  statusCd: undefined,
  statusDate: [],
  createStaff: undefined,
  createDate: [],
  remark: undefined,
  wcUserId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await IdentifyApi.getIdentifyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await IdentifyApi.deleteIdentify(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await IdentifyApi.exportIdentify(queryParams)
    download.excel(data, '实名认证.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>