#!/bin/bash

# Jenkins 构建脚本 - 针对 5GB 内存限制优化
# 作者: AI Assistant
# 用途: 在内存受限的 Jenkins 环境中构建 Vue3 项目

set -e  # 遇到错误立即退出

echo "🚀 开始 Jenkins 构建流程..."

# 1. 清理缓存和临时文件
echo "📦 清理缓存..."
rm -rf node_modules/.cache
rm -rf dist-prod
rm -rf .vite

# 2. 设置 Node.js 环境变量以优化内存使用
export NODE_OPTIONS="--max_old_space_size=3584 --optimize-for-size"
export NODE_ENV=production

# 3. 清理 npm 缓存
echo "🧹 清理 npm 缓存..."
npm cache clean --force

# 4. 检查可用内存
echo "💾 检查系统内存..."
free -h

# 5. 构建项目
echo "🔨 开始构建..."
npm run build:prod

# 6. 检查构建结果
if [ -d "dist-prod" ]; then
    echo "✅ 构建成功！"
    echo "📁 构建产物大小:"
    du -sh dist-prod
    echo "📄 文件数量:"
    find dist-prod -type f | wc -l
else
    echo "❌ 构建失败！"
    exit 1
fi

echo "🎉 Jenkins 构建完成！"
