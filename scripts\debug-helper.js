#!/usr/bin/env node

/**
 * 调试助手脚本
 * 用于快速启动不同环境的调试配置
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 环境配置
const environments = {
  local: {
    name: '本地开发环境',
    port: 3000,
    env: '.env.local',
    command: 'pnpm dev',
    url: 'http://localhost:3000'
  },
  dev: {
    name: '开发服务器环境',
    port: 3000,
    env: '.env.dev',
    command: 'pnpm dev-server',
    url: 'http://localhost:3000'
  },
  prod: {
    name: '生产环境',
    port: 80,
    env: '.env.prod',
    command: 'pnpm preview',
    url: 'http://localhost'
  }
};

function showHelp() {
  console.log(`
🚀 调试助手 - 文创链2.0

用法: node scripts/debug-helper.js [环境]

可用环境:
  local   - 本地开发环境 (端口: 3000, 配置: .env.local)
  dev     - 开发服务器环境 (端口: 3000, 配置: .env.dev)  
  prod    - 生产环境 (端口: 80, 配置: .env.prod)

示例:
  node scripts/debug-helper.js local
  node scripts/debug-helper.js dev

如果不指定环境，默认使用本地开发环境。
  `);
}

function checkEnvironmentFile(envFile) {
  const envPath = path.join(process.cwd(), envFile);
  if (!fs.existsSync(envPath)) {
    console.error(`❌ 环境配置文件不存在: ${envFile}`);
    return false;
  }
  return true;
}

function startDebugEnvironment(envKey) {
  const env = environments[envKey];
  if (!env) {
    console.error(`❌ 未知环境: ${envKey}`);
    showHelp();
    process.exit(1);
  }

  console.log(`🚀 启动${env.name}...`);
  console.log(`📁 配置文件: ${env.env}`);
  console.log(`🌐 访问地址: ${env.url}`);
  console.log(`⚡ 启动命令: ${env.command}`);

  // 检查环境配置文件
  if (!checkEnvironmentFile(env.env)) {
    process.exit(1);
  }

  // 启动开发服务器
  const [cmd, ...args] = env.command.split(' ');
  const child = spawn(cmd, args, {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd()
  });

  child.on('error', (error) => {
    console.error(`❌ 启动失败: ${error.message}`);
    process.exit(1);
  });

  child.on('close', (code) => {
    if (code !== 0) {
      console.error(`❌ 进程退出，代码: ${code}`);
    }
  });

  // 处理退出信号
  process.on('SIGINT', () => {
    console.log('\n🛑 正在停止服务器...');
    child.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 正在停止服务器...');
    child.kill('SIGTERM');
    process.exit(0);
  });

  // 延迟显示调试提示
  setTimeout(() => {
    console.log(`
✅ 服务器已启动！

🔧 调试步骤:
1. 在 VSCode 中按 F5 或点击"运行和调试"
2. 选择"调试：${env.name} (${env.port}端口)"
3. 或者选择"启动并调试：${env.name}"自动启动

🌐 浏览器访问: ${env.url}
📝 配置文件: ${env.env}
🛑 停止服务: Ctrl+C
    `);
  }, 3000);
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const envKey = args[0] || 'local';
  startDebugEnvironment(envKey);
}

if (require.main === module) {
  main();
}

module.exports = {
  environments,
  startDebugEnvironment
};
