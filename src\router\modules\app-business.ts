import { Layout } from '@/utils/routerHelper'
import { AppRouteRecordRaw } from '@/types/router'

const appBusinessRouter: AppRouteRecordRaw = {
  path: '/business',
  component: Layout,
  redirect: '/business/index',
  name: 'AppBusiness',
  meta: {
    title: '业务服务',
    icon: 'ep:briefcase',
    alwaysShow: true
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/business/app/index.vue'),
      name: 'AppBusinessIndex',
      meta: {
        title: '业务大厅',
        icon: 'ep:shop',
        noCache: false
      }
    },
    {
      path: 'type/:businessType',
      component: () => import('@/views/business/app/business-type/index.vue'),
      name: 'AppBusinessType',
      meta: {
        title: '业务详情',
        icon: 'ep:document',
        noCache: true,
        hidden: true,
        activeMenu: '/business/index'
      }
    },
    {
      path: 'config/:configId',
      component: () => import('@/views/business/app/config-detail/index.vue'),
      name: 'AppBusinessConfigDetail',
      meta: {
        title: '配置详情',
        icon: 'ep:view',
        noCache: true,
        hidden: true,
        activeMenu: '/business/index'
      }
    },
    {
      path: 'order/create',
      component: () => import('@/views/business/app/order/create.vue'),
      name: 'AppBusinessOrderCreate',
      meta: {
        title: '创建订单',
        icon: 'ep:plus',
        noCache: true,
        hidden: true,
        activeMenu: '/business/orders'
      }
    },
    {
      path: 'orders',
      component: () => import('@/views/business/app/order/index.vue'),
      name: 'AppBusinessOrders',
      meta: {
        title: '我的订单',
        icon: 'ep:list',
        noCache: false
      }
    },
    {
      path: 'order/:orderId',
      component: () => import('@/views/business/app/order/detail.vue'),
      name: 'AppBusinessOrderDetail',
      meta: {
        title: '订单详情',
        icon: 'ep:view',
        noCache: true,
        hidden: true,
        activeMenu: '/business/orders'
      }
    },
    {
      path: 'progress/:orderId',
      component: () => import('@/views/business/app/progress/index.vue'),
      name: 'AppBusinessProgress',
      meta: {
        title: '业务进度',
        icon: 'ep:trend-charts',
        noCache: true,
        hidden: true,
        activeMenu: '/business/orders'
      }
    },
    {
      path: 'compare',
      component: () => import('@/views/business/app/compare/index.vue'),
      name: 'AppBusinessCompare',
      meta: {
        title: '服务对比',
        icon: 'ep:scale-to-original',
        noCache: true,
        hidden: true,
        activeMenu: '/business/index'
      }
    }
  ]
}

export default appBusinessRouter
