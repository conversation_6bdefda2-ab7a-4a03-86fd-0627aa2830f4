/**
 * Commitlint 配置文件
 * 
 * 用于规范 Git 提交信息格式，确保提交信息的一致性和可读性
 * 
 * 提交信息格式：<type>(<scope>): <subject>
 * 
 * 示例：
 * - feat(user): 添加用户管理功能
 * - fix(login): 修复登录页面样式问题
 * - docs(readme): 更新项目文档
 * - style(button): 调整按钮样式
 * - refactor(api): 重构用户API接口
 * - test(utils): 添加工具函数单元测试
 * - chore(deps): 更新依赖包版本
 */

module.exports = {
  // 继承 conventional 规范
  extends: ['@commitlint/config-conventional'],
  
  // 自定义规则
  rules: {
    // type 类型定义，表示 git 提交的 type 必须在以下类型范围内
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能 feature
        'add',      // 添加功能
        'fix',      // 修复 bug
        'docs',     // 文档注释
        'update',   // 更新文件
        'style',    // 代码格式(不影响代码运行的变动)
        'refactor', // 重构(既不增加新功能，也不是修复bug)
        'perf',     // 性能优化
        'test',     // 增加测试
        'chore',    // 构建过程或辅助工具的变动
        'revert',   // 回退
        'build',    // 打包
        'ci',       // CI/CD 相关
        'release',  // 发布版本
        'workflow', // 工作流
        'types',    // 类型定义
        'wip'       // 开发中
      ]
    ],
    // subject 大小写不做校验
    'subject-case': [0],
    // subject 不允许为空
    'subject-empty': [2, 'never'],
    // subject 最大长度
    'subject-max-length': [2, 'always', 100],
    // type 不允许为空
    'type-empty': [2, 'never'],
    // scope 可以为空
    'scope-empty': [0, 'never'],
    // scope 大小写不做校验
    'scope-case': [0],
    // header 最大长度
    'header-max-length': [2, 'always', 120]
  },
  
  // 忽略规则
  ignores: [
    // 忽略 merge 提交
    (message) => message.includes('Merge'),
    // 忽略 revert 提交
    (message) => message.includes('Revert'),
    // 忽略初始化提交
    (message) => message.includes('Initial commit')
  ],
  
  // 自定义提示信息
  prompt: {
    messages: {
      type: '选择你要提交的类型:',
      scope: '选择一个提交范围 (可选):',
      customScope: '请输入自定义的提交范围:',
      subject: '填写简短精炼的变更描述:',
      body: '填写更加详细的变更描述 (可选)。使用 "|" 换行:',
      breaking: '列举非兼容性重大的变更 (可选):',
      footer: '列举出所有变更的 ISSUES CLOSED (可选)。 例如: #31, #34:',
      confirmCommit: '确认提交?'
    },
    types: [
      { value: 'feat', name: 'feat:     ✨ 新增功能' },
      { value: 'fix', name: 'fix:      🐛 修复缺陷' },
      { value: 'docs', name: 'docs:     📝 文档变更' },
      { value: 'style', name: 'style:    💄 代码格式' },
      { value: 'refactor', name: 'refactor: ♻️  代码重构' },
      { value: 'perf', name: 'perf:     ⚡️ 性能优化' },
      { value: 'test', name: 'test:     ✅ 添加疏漏测试或已有测试改动' },
      { value: 'build', name: 'build:    📦️ 构建流程、外部依赖变更' },
      { value: 'ci', name: 'ci:       🎡 修改 CI 配置、脚本' },
      { value: 'chore', name: 'chore:    🔨 对构建过程或辅助工具和库的更改' },
      { value: 'revert', name: 'revert:   ⏪️ 回滚 commit' },
      { value: 'wip', name: 'wip:      🚧 开发中' }
    ],
    useEmoji: true,
    emojiAlign: 'center',
    allowCustomScopes: true,
    allowEmptyScopes: true,
    customScopesAlign: 'bottom',
    customScopesAlias: 'custom',
    emptyScopesAlias: 'empty',
    upperCaseSubject: false
  }
}
