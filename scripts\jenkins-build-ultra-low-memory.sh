#!/bin/bash

# Jenkins超低内存构建脚本
# 专门为极度内存受限环境设计（1GB以下可用内存）

set -e

echo "🚀 开始超低内存模式构建..."
echo "⚠️  警告：此模式将禁用大部分优化以节省内存"

# 设置极限内存限制
export NODE_OPTIONS="--max_old_space_size=768 --max-semi-space-size=16"
export VITE_BUILD_CHUNK_SIZE_LIMIT=300
export VITE_DROP_CONSOLE=true
export VITE_DROP_DEBUGGER=true
export VITE_SOURCEMAP=false
export VITE_OUT_DIR=dist-prod
export VITE_BUILD_MINIFY=false

echo "📊 当前内存状态:"
free -h

echo "🧹 清理所有缓存和临时文件..."
rm -rf node_modules/.cache || true
rm -rf node_modules/.vite || true
rm -rf .vite || true
rm -rf .swc || true
rm -rf dist-prod || true
rm -rf core.* || true
rm -rf .nuxt || true
rm -rf .output || true

# 清理npm缓存
npm cache clean --force || true

# 强制垃圾回收（如果可能）
sync
echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true

echo "📊 清理后内存状态:"
free -h

echo "🔧 设置Git配置以减少内存使用..."
git config --local core.preloadindex false
git config --local core.fscache false
git config --local gc.auto 0

echo "⚡ 使用超低内存配置文件构建..."
echo "当前内存配置: NODE_OPTIONS=$NODE_OPTIONS"

# 使用专门的超低内存配置文件
node --max_old_space_size=768 \
     --max-semi-space-size=16 \
     --expose-gc \
     ./node_modules/vite/bin/vite.js build \
     --config vite.config.ultra-low-memory.ts \
     --mode prod

echo "✅ 构建完成!"

echo "📊 构建后内存状态:"
free -h

echo "📁 构建产物信息:"
if [ -d "dist-prod" ]; then
    echo "构建成功！"
    echo "构建产物大小: $(du -sh dist-prod)"
    echo "文件数量: $(find dist-prod -type f | wc -l)"
    
    # 列出构建文件
    echo "构建文件列表:"
    find dist-prod -type f -exec ls -lh {} \; | head -20
    
    echo ""
    echo "⚠️  注意：此构建未经过代码压缩，文件较大"
    echo "💡 建议：在生产环境部署前可以使用其他工具进行压缩"
else
    echo "构建失败！dist-prod目录不存在"
    exit 1
fi

echo "🎉 超低内存构建流程完成!"
