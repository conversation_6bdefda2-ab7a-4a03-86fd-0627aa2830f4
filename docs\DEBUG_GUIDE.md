# VSCode 调试配置指南

## 🚀 快速开始

### 方法一：自动启动并调试（推荐）
1. 在 VSCode 中按 `F5` 或点击"运行和调试"
2. 选择 `启动并调试：本地开发环境`
3. 系统会自动启动开发服务器并连接调试器

### 方法二：手动启动后调试
1. 先启动开发服务器：`pnpm dev`
2. 等待服务器完全启动（看到 "Local: http://localhost:3000" 提示）
3. 在 VSCode 中选择 `快速调试：连接到3000端口`

## 🔧 调试配置说明

### 可用的调试配置

| 配置名称 | 浏览器 | 端口 | 用途 |
|---------|--------|------|------|
| `调试：本地开发环境 (3000端口)` | Edge | 3000 | 本地开发环境调试 |
| `调试：本地开发环境 - Chrome (3000端口)` | Chrome | 3000 | 使用 Chrome 调试 |
| `调试：开发服务器环境 (3000端口)` | Edge | 3000 | 连接远程后端调试 |
| `调试：生产环境 (80端口)` | Edge | 80 | 生产环境调试 |
| `快速调试：连接到3000端口` | Edge | 3000 | 快速连接已运行服务器 |
| `附加到现有浏览器` | Edge | 9222 | 附加到已打开的浏览器 |

### 复合调试配置

- `启动并调试：本地开发环境` - 自动启动本地服务器并调试
- `启动并调试：开发服务器环境` - 自动启动开发服务器并调试

## 🛠️ 故障排除

### 问题1：无法附加到浏览器

**症状**：点击调试后提示"无法附加到浏览器"或连接超时

**解决方案**：
1. **确保服务器已启动**
   ```bash
   pnpm dev
   # 等待看到 "Local: http://localhost:3000" 提示
   ```

2. **检查端口是否被占用**
   ```bash
   # Windows
   netstat -ano | findstr :3000
   
   # 如果端口被占用，杀死进程或更换端口
   ```

3. **使用快速调试配置**
   - 选择 `快速调试：连接到3000端口`
   - 这个配置有更短的超时时间和更简单的启动参数

4. **清理浏览器调试数据**
   ```bash
   # 删除调试用户数据目录
   rm -rf .vscode/edge-debug
   rm -rf .vscode/chrome-debug
   ```

### 问题2：断点无法命中

**症状**：设置断点后，代码执行时不会停在断点处

**解决方案**：
1. **检查 sourcemap 配置**
   - 确保 `.env.local` 中 `VITE_SOURCEMAP=true`
   - 重启开发服务器

2. **检查文件路径映射**
   - 确保 `webRoot` 设置为 `${workspaceFolder}/src`
   - 断点应该设置在 `src` 目录下的源文件中

3. **清除浏览器缓存**
   - 在浏览器中按 `Ctrl+Shift+R` 强制刷新
   - 或在开发者工具中禁用缓存

### 问题3：调试器启动缓慢

**症状**：点击调试后需要等待很长时间才能连接

**解决方案**：
1. **使用等待脚本**
   ```bash
   node scripts/wait-for-server.js 3000
   ```

2. **手动启动服务器**
   - 先运行 `pnpm dev`
   - 等待完全启动后再开始调试

3. **增加超时时间**
   - 调试配置中的 `timeout` 已设置为 30000ms
   - 如果仍然超时，检查网络和系统性能

### 问题4：环境变量不生效

**症状**：调试时环境变量与预期不符

**解决方案**：
1. **检查环境文件**
   ```bash
   # 本地开发使用
   cat .env.local
   
   # 开发服务器使用
   cat .env.dev
   ```

2. **重启 VSCode**
   - 环境变量更改后需要重启 VSCode
   - 或重新加载窗口：`Ctrl+Shift+P` → "Developer: Reload Window"

## 📝 调试技巧

### 1. 使用调试助手脚本
```bash
# 启动本地开发环境
node scripts/debug-helper.js local

# 启动开发服务器环境  
node scripts/debug-helper.js dev
```

### 2. 检查服务器状态
```bash
# 检查服务器是否响应
curl http://localhost:3000

# 或使用等待脚本
node scripts/wait-for-server.js 3000
```

### 3. 查看调试日志
- 在 VSCode 输出面板中选择"调试控制台"
- 查看浏览器开发者工具的控制台
- 检查 VSCode 的"输出"面板中的"调试适配器"日志

### 4. 使用不同浏览器
- 如果 Edge 有问题，尝试使用 Chrome 配置
- 不同浏览器的调试行为可能略有不同

## 🔍 高级调试

### 1. 网络请求调试
- 在浏览器开发者工具的"网络"标签页查看 API 请求
- 设置网络断点调试 AJAX 请求

### 2. Vue DevTools
- 安装 Vue DevTools 浏览器扩展
- 在组件标签页查看组件状态和属性

### 3. 性能调试
- 使用浏览器的"性能"标签页分析性能问题
- 在 Vite 配置中启用性能分析

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **检查项目依赖**
   ```bash
   pnpm install
   ```

2. **重置开发环境**
   ```bash
   pnpm clean
   pnpm install
   ```

3. **查看详细错误信息**
   - 在 VSCode 的"输出"面板查看详细日志
   - 在浏览器控制台查看错误信息

4. **联系开发团队**
   - 提供详细的错误信息和复现步骤
   - 包含系统环境信息（操作系统、Node.js 版本等）
