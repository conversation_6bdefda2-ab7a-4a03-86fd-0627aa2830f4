import request from '@/config/axios'

// 数据存证审核信息响应 VO
export interface CertificateReviewRespVO {
  id: number // 主键
  reviewDataId: number // 数据存证id，关联数据存证的数据编号、存证内容等
  reviewOrgId: number // 审查机构id
  reviewDate: number // 审查时间（时间戳）
  reviewResults: string // 审查结果
  reviewDataSubjectComp: number // 审查数据权益主体合规性，0不合规，1合规
  reviewDataSourceComp: number // 审查数据来源合规性，0不合规，1合规
  reviewDataProcessComp: number // 审查数据处理合规性，0不合规，1合规
  reviewDataContentComp: number // 审查数据内容合规性，0不合规，1合规
  statusCd: string // 状态
  remark: string // 备注
  creator: string // 创建者ID
  createTime: string // 创建时间
  updater: string // 更新者ID
  updateTime: string // 更新时间
}

// 数据存证审核信息保存请求 VO
export interface CertificateReviewSaveReqVO {
  id?: number // 主键
  reviewDataId: number // 数据存证id，关联数据存证的数据编号、存证内容等
  reviewOrgId: number // 审查机构id
  reviewDate?: number // 审查时间（时间戳）
  reviewResults?: string // 审查结果
  reviewDataSubjectComp?: number // 审查数据权益主体合规性，0不合规，1合规
  reviewDataSourceComp?: number // 审查数据来源合规性，0不合规，1合规
  reviewDataProcessComp?: number // 审查数据处理合规性，0不合规，1合规
  reviewDataContentComp?: number // 审查数据内容合规性，0不合规，1合规
  statusCd?: string // 状态
  remark?: string // 备注
}

// 数据存证审核信息分页查询参数
export interface CertificateReviewPageReqVO {
  reviewDataId?: number // 数据存证id，关联数据存证的数据编号、存证内容等
  reviewOrgId?: number // 审查机构id
  reviewDate?: number[] // 审查时间（时间戳数组）
  reviewResults?: string // 审查结果
  reviewDataSubjectComp?: number // 审查数据权益主体合规性，0不合规，1合规
  reviewDataSourceComp?: number // 审查数据来源合规性，0不合规，1合规
  reviewDataProcessComp?: number // 审查数据处理合规性，0不合规，1合规
  reviewDataContentComp?: number // 审查数据内容合规性，0不合规，1合规
  statusCd?: string // 状态
  remark?: string // 备注
  creator?: string // 创建者ID
  createTime?: string[] // 创建时间
  updater?: string // 更新者ID
  updateTime?: string[] // 更新时间
  pageNo: number // 页码，从 1 开始
  pageSize: number // 每页条数，最大值为 100
}

// 数据存证审核信息 API
export const CertificateReviewApi = {
  // 获得数据存证审核信息分页列表
  getCertificateReviewPage: async (params: CertificateReviewPageReqVO) => {
    return await request.get({ url: `/business/certificate-review/page`, params })
  },

  // 获得数据存证审核信息详情
  getCertificateReview: async (id: number) => {
    return await request.get({ url: `/business/certificate-review/get`, params: { id } })
  },

  // 创建数据存证审核信息
  createCertificateReview: async (data: CertificateReviewSaveReqVO) => {
    return await request.post({ url: `/business/certificate-review/create`, data })
  },

  // 更新数据存证审核信息
  updateCertificateReview: async (data: CertificateReviewSaveReqVO) => {
    return await request.put({ url: `/business/certificate-review/update`, data })
  },

  // 删除数据存证审核信息
  deleteCertificateReview: async (id: number) => {
    return await request.delete({ url: `/business/certificate-review/delete`, params: { id } })
  },

  // 导出数据存证审核信息 Excel
  exportCertificateReview: async (params: CertificateReviewPageReqVO) => {
    return await request.download({ url: `/business/certificate-review/export-excel`, params })
  }
}
