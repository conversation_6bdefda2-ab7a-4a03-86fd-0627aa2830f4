<template>
  <Dialog
    v-model="dialogVisible"
    title="业务进度"
    width="700px"
    @close="handleClose"
  >
    <div v-if="orderData" class="business-progress-container">
      <!-- 进度概览 -->
      <div class="progress-overview">
        <div class="progress-header">
          <div class="order-info">
            <h3>{{ orderData.orderInfo?.orderNo }}</h3>
            <div class="business-type">
              <Icon :icon="getBusinessTypeConfig(orderData.businessType)?.icon" class="mr-2" />
              {{ getBusinessTypeName(orderData.businessType) }}
            </div>
          </div>
          <div class="current-status">
            <el-tag
              :type="getBusinessStatusConfig(orderData.businessStatus)?.color"
              size="large"
            >
              {{ getBusinessStatusName(orderData.businessStatus) }}
            </el-tag>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-bar">
          <el-steps :active="currentStep" align-center>
            <el-step
              v-for="step in progressSteps"
              :key="step.status"
              :title="step.title"
              :description="step.description"
              :icon="step.icon"
              :status="step.stepStatus"
            />
          </el-steps>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="time-info">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="time-item">
              <div class="time-label">创建时间</div>
              <div class="time-value">{{ formatDate(orderData.createTime) }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="time-item">
              <div class="time-label">预计完成</div>
              <div class="time-value">{{ estimatedCompletionTime }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="time-item">
              <div class="time-label">实际完成</div>
              <div class="time-value">{{ actualCompletionTime || '未完成' }}</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 进度详情 -->
      <div class="progress-details">
        <h4>进度详情</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in progressHistory"
            :key="index"
            :timestamp="item.time"
            :type="item.type"
            :icon="item.icon"
            :color="item.color"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ item.title }}</div>
              <div v-if="item.description" class="timeline-description">
                {{ item.description }}
              </div>
              <div v-if="item.operator" class="timeline-operator">
                操作人：{{ item.operator }}
              </div>
              <div v-if="item.duration" class="timeline-duration">
                耗时：{{ item.duration }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 处理统计 -->
      <div class="processing-stats">
        <h4>处理统计</h4>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic
              title="总耗时"
              :value="processingStats.totalDuration"
              suffix="小时"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="处理步骤"
              :value="processingStats.completedSteps"
              :suffix="`/${processingStats.totalSteps}`"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="完成率"
              :value="processingStats.completionRate"
              suffix="%"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="剩余时间"
              :value="processingStats.remainingTime"
              suffix="小时"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 相关文件 -->
      <div v-if="relatedFiles.length" class="related-files">
        <h4>相关文件</h4>
        <div class="file-list">
          <div
            v-for="file in relatedFiles"
            :key="file.id"
            class="file-item"
          >
            <div class="file-icon">
              <Icon :icon="getFileIcon(file.type)" />
            </div>
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                {{ file.size }} • {{ formatDate(file.uploadTime) }}
              </div>
            </div>
            <div class="file-actions">
              <el-button type="primary" link @click="downloadFile(file)">
                <Icon icon="ep:download" />
              </el-button>
              <el-button type="primary" link @click="previewFile(file)">
                <Icon icon="ep:view" />
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleRefresh">
        <Icon icon="ep:refresh" class="mr-1" />
        刷新进度
      </el-button>
      <el-button type="success" @click="handleExport">
        <Icon icon="ep:download" class="mr-1" />
        导出报告
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts" name="BusinessProgress">
import { BusinessOrderRelationTableItem } from '@/types/business/businesscfg/order-relation'
import {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getBusinessStatusName,
  getBusinessStatusConfig,
  calculateEstimatedCompletionTime
} from '@/utils/business/businesscfg/helpers'
import { formatDate } from '@/utils/formatTime'

// 响应式数据
const dialogVisible = ref(false)
const orderData = ref<BusinessOrderRelationTableItem | null>(null)

// 进度步骤定义
const progressSteps = ref([
  {
    status: 'PENDING',
    title: '待处理',
    description: '等待处理',
    icon: 'ep:clock',
    stepStatus: 'wait'
  },
  {
    status: 'PROCESSING',
    title: '处理中',
    description: '正在处理',
    icon: 'ep:loading',
    stepStatus: 'wait'
  },
  {
    status: 'COMPLETED',
    title: '已完成',
    description: '处理完成',
    icon: 'ep:check',
    stepStatus: 'wait'
  }
])

// 进度历史
const progressHistory = ref([
  {
    time: '2024-01-15 10:00:00',
    type: 'primary',
    icon: 'ep:plus',
    color: '#409eff',
    title: '订单创建',
    description: '用户提交业务申请，系统自动创建订单',
    operator: '系统',
    duration: '0分钟'
  },
  {
    time: '2024-01-15 10:05:00',
    type: 'warning',
    icon: 'ep:clock',
    color: '#e6a23c',
    title: '进入待处理队列',
    description: '订单进入待处理队列，等待工作人员处理',
    operator: '系统',
    duration: '5分钟'
  }
])

// 相关文件
const relatedFiles = ref([
  {
    id: 1,
    name: '申请材料.pdf',
    type: 'pdf',
    size: '2.5MB',
    uploadTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    name: '身份证明.jpg',
    type: 'image',
    size: '1.2MB',
    uploadTime: '2024-01-15 10:01:00'
  }
])

// 计算属性
const currentStep = computed(() => {
  if (!orderData.value) return 0
  
  const statusIndex = progressSteps.value.findIndex(
    step => step.status === orderData.value!.businessStatus
  )
  return statusIndex >= 0 ? statusIndex : 0
})

const estimatedCompletionTime = computed(() => {
  if (!orderData.value || !orderData.value.configInfo?.processingDays) {
    return '未知'
  }
  
  const estimated = calculateEstimatedCompletionTime(
    orderData.value.createTime,
    orderData.value.configInfo.processingDays
  )
  return formatDate(estimated)
})

const actualCompletionTime = computed(() => {
  if (!orderData.value || orderData.value.businessStatus !== 'COMPLETED') {
    return null
  }
  return formatDate(orderData.value.updateTime)
})

const processingStats = computed(() => {
  const totalSteps = progressSteps.value.length
  const completedSteps = currentStep.value + 1
  const completionRate = Math.round((completedSteps / totalSteps) * 100)
  
  // 计算总耗时（从创建到现在）
  const createTime = new Date(orderData.value?.createTime || Date.now())
  const now = new Date()
  const totalDuration = Math.round((now.getTime() - createTime.getTime()) / (1000 * 60 * 60))
  
  // 估算剩余时间
  const processingDays = orderData.value?.configInfo?.processingDays || 1
  const expectedDuration = processingDays * 24
  const remainingTime = Math.max(0, expectedDuration - totalDuration)
  
  return {
    totalDuration,
    completedSteps,
    totalSteps,
    completionRate,
    remainingTime
  }
})

// 更新进度步骤状态
const updateStepStatus = () => {
  if (!orderData.value) return
  
  progressSteps.value.forEach((step, index) => {
    if (index < currentStep.value) {
      step.stepStatus = 'finish'
    } else if (index === currentStep.value) {
      step.stepStatus = orderData.value!.businessStatus === 'FAILED' ? 'error' : 'process'
    } else {
      step.stepStatus = 'wait'
    }
  })
}

// 获取文件图标
const getFileIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'ep:document',
    image: 'ep:picture',
    doc: 'ep:document',
    excel: 'ep:document',
    zip: 'ep:folder'
  }
  return iconMap[type] || 'ep:document'
}

// 打开弹窗
const open = (data: BusinessOrderRelationTableItem) => {
  orderData.value = data
  dialogVisible.value = true
  
  // 更新步骤状态
  updateStepStatus()
  
  // 加载进度数据
  loadProgressData(data.id)
}

// 加载进度数据
const loadProgressData = async (id: number) => {
  try {
    // 这里应该调用API获取进度数据
    // 暂时使用模拟数据
    console.log('加载进度数据:', id)
  } catch (error) {
    console.error('加载进度数据失败:', error)
  }
}

// 下载文件
const downloadFile = (file: any) => {
  // 实现文件下载逻辑
  console.log('下载文件:', file)
  ElMessage.success(`开始下载 ${file.name}`)
}

// 预览文件
const previewFile = (file: any) => {
  // 实现文件预览逻辑
  console.log('预览文件:', file)
  ElMessage.info(`预览 ${file.name}`)
}

// 刷新进度
const handleRefresh = () => {
  if (orderData.value) {
    loadProgressData(orderData.value.id)
    ElMessage.success('进度已刷新')
  }
}

// 导出报告
const handleExport = () => {
  if (!orderData.value) return
  
  const reportData = {
    orderInfo: {
      orderNo: orderData.value.orderInfo?.orderNo,
      businessType: getBusinessTypeName(orderData.value.businessType),
      businessStatus: getBusinessStatusName(orderData.value.businessStatus)
    },
    progressInfo: {
      currentStep: currentStep.value,
      totalSteps: progressSteps.value.length,
      completionRate: processingStats.value.completionRate
    },
    timeInfo: {
      createTime: orderData.value.createTime,
      estimatedCompletion: estimatedCompletionTime.value,
      actualCompletion: actualCompletionTime.value
    },
    progressHistory: progressHistory.value,
    processingStats: processingStats.value
  }
  
  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `progress-report-${orderData.value.id}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('进度报告导出成功')
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  orderData.value = null
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.business-progress-container {
  padding: 20px;
}

.progress-overview {
  margin-bottom: 32px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.order-info h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.business-type {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.progress-bar {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
}

.time-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.time-item {
  text-align: center;
}

.time-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.time-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.progress-details {
  margin-bottom: 32px;
}

.progress-details h4 {
  margin-bottom: 16px;
  color: #333;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.timeline-operator {
  color: #999;
  font-size: 12px;
  margin-bottom: 2px;
}

.timeline-duration {
  color: #409eff;
  font-size: 12px;
  font-weight: 500;
}

.processing-stats {
  margin-bottom: 32px;
}

.processing-stats h4 {
  margin-bottom: 16px;
  color: #333;
}

.related-files h4 {
  margin-bottom: 16px;
  color: #333;
}

.file-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 4px;
  margin-bottom: 8px;
  border: 1px solid #e1e8ed;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #409eff;
  color: white;
  border-radius: 4px;
  margin-right: 12px;
  font-size: 18px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 8px;
}
</style>
