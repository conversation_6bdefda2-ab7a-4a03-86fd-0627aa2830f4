import request from '@/config/axios'

// 存证审核信息 VO
export interface CertificateReviewVO {
  id: number // 主键
  reviewDataId: number // 数据存证id，关联数据存证的数据编号、存证内容等
  reviewOrgId: number // 审查机构id
  reviewDate: number // 审查时间（时间戳）
  reviewResults: string // 审查结果
  reviewDataSubjectComp: number // 审查数据权益主体合规性，0不合规，1合规
  reviewDataSourceComp: number // 审查数据来源合规性，0不合规，1合规
  reviewDataProcessComp: number // 审查数据处理合规性，0不合规，1合规
  reviewDataContentComp: number // 审查数据内容合规性，0不合规，1合规
  statusCd: string // 状态
  remark: string // 备注
  creator: string // 创建者ID
  createTime: string // 创建时间
  updater: string // 更新者ID
  updateTime: string // 更新时间
}

// 存证信息表，用于记录各类数据存证相关信息 VO
export interface CertificateInfoVO {
  id: number // 主键
  dataNumber: string // 数据编号
  dataName: string // 数据名称
  certificationUnit: string // 存证单位
  dataClassification: string // 数据分类
  dataLevel: string // 数据级别
  dataGenerationTime: Date // 数据生成时间
  dataScale: string // 数据规模
  dataScaleUnit: string // 数据规模单位
  dataResourceForm: string // 数据资源形态
  dataResourceOwnership: number // 数据资源权属申明
  dataSampleFile: string // 数据样本文件路径
  restrictionDescription: string // 限制情况说明
  otherDescription: string // 其他描述
  dataBlockHashValue: string // 数据块哈希值
  dataAccessAddress: string // 数据访问地址
  extendedInfoHashValue: string // 扩展信息哈希值
  extendedInfoAccessAddress: string // 扩展信息访问地址
  dataSourceInfo: string // 数据来源信息
  dataSourceSpecificInfo: string // 数据来源具体信息
  dataSourceEvidenceMaterial: string // 数据来源佐证材料路径
  effectiveControlEvidenceMaterial: string // 有效控制措施佐证材料路径
  personalInfoCollectionEvidenceMaterial: string // 个人信息采集的合法佐证材料路径
  remark: string // 备注
  createTime: string // 创建时间
  creator: string // 创建人
  updater: string // 更新人
  updateTime: string // 更新时间
  certificateReviews: CertificateReviewVO[] // 关联的存证审核记录列表
}

// 根据多个状态条件获得存证信息分页查询参数
export interface CertificateInfoPageByStatusReqVO {
  pageNo: number // 页码，从 1 开始
  pageSize: number // 每页条数，最大值为 100
  dataNumber?: string // 数据编号
  dataName?: string // 数据名称
  certificationUnit?: string // 存证单位
  dataClassification?: string // 数据分类
  dataLevel?: string // 数据级别
  dataGenerationTime?: string[] // 数据生成时间
  dataScale?: string // 数据规模
  dataScaleUnit?: string // 数据规模单位
  dataResourceForm?: string // 数据资源形态
  dataResourceOwnership?: string // 数据资源权属申明(JSON格式)
  dataSampleFile?: string // 数据样本文件路径
  restrictionDescription?: string // 限制情况说明
  otherDescription?: string // 其他描述
  dataBlockHashValue?: string // 数据块哈希值
  dataAccessAddress?: string // 数据访问地址
  extendedInfoHashValue?: string // 扩展信息哈希值
  extendedInfoAccessAddress?: string // 扩展信息访问地址
  dataSourceInfo?: string // 数据来源信息
  dataSourceSpecificInfo?: string // 数据来源具体信息
  dataSourceEvidenceMaterial?: string // 数据来源佐证材料路径
  effectiveControlEvidenceMaterial?: string // 有效控制措施佐证材料路径
  personalInfoCollectionEvidenceMaterial?: string // 个人信息采集的合法佐证材料路径
  statusCd?: string // 状态
  statusCdList?: string[] // 状态列表（用于多状态条件查询）
  remark?: string // 备注
  createTime?: string[] // 创建时间
}

// 存证信息表，用于记录各类数据存证相关信息 API
export const CertificateInfoApi = {
  // 查询存证信息表，用于记录各类数据存证相关信息分页
  getCertificateInfoPage: async (params: any) => {
    return await request.get({ url: `/business/certificate-info/page`, params })
  },

  // 根据多个状态条件获得存证信息分页
  getCertificateInfoPageByStatus: async (params: CertificateInfoPageByStatusReqVO) => {
    return await request.get({ url: `/business/certificate-info/page-by-status`, params })
  },

  // 查询存证信息表，用于记录各类数据存证相关信息详情
  getCertificateInfo: async (id: number) => {
    return await request.get({ url: `/business/certificate-info/get?id=` + id })
  },

  // 新增存证信息表，用于记录各类数据存证相关信息
  createCertificateInfo: async (data: CertificateInfoVO) => {
    return await request.post({ url: `/business/certificate-info/create`, data })
  },

  // 修改存证信息表，用于记录各类数据存证相关信息
  updateCertificateInfo: async (data: CertificateInfoVO) => {
    return await request.put({ url: `/business/certificate-info/update`, data })
  },

  // 删除存证信息表，用于记录各类数据存证相关信息
  deleteCertificateInfo: async (id: number) => {
    return await request.delete({ url: `/business/certificate-info/delete?id=` + id })
  },

  // 导出存证信息表，用于记录各类数据存证相关信息 Excel
  exportCertificateInfo: async (params: CertificateInfoPageByStatusReqVO) => {
    return await request.download({ url: `/business/certificate-info/export-excel`, params })
  }
}
