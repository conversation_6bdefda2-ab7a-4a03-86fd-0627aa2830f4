import { 
  AppBusinessTypeEnum, 
  AppBusinessStatusEnum, 
  AppOrderStatusEnum,
  AppPriceDisplayItem,
  AppBusinessCard,
  AppPriceCard,
  AppOrderCard
} from '@/types/business/businesscfg/app-business'

import {
  APP_BUSINESS_TYPE_CONFIG,
  APP_ORDER_STATUS_CONFIG,
  APP_BUSINESS_STATUS_CONFIG,
  APP_MEMBER_LEVEL_CONFIG,
  APP_BUSINESS_RULES
} from './app-constants'

// ==================== 业务类型相关函数 ====================

/**
 * 获取业务类型名称
 */
export function getAppBusinessTypeName(businessType: string): string {
  const config = APP_BUSINESS_TYPE_CONFIG[businessType as AppBusinessTypeEnum]
  return config?.name || businessType
}

/**
 * 获取业务类型配置
 */
export function getAppBusinessTypeConfig(businessType: string) {
  return APP_BUSINESS_TYPE_CONFIG[businessType as AppBusinessTypeEnum]
}

/**
 * 获取业务类型选项列表
 */
export function getAppBusinessTypeOptions() {
  return Object.entries(APP_BUSINESS_TYPE_CONFIG).map(([code, config]) => ({
    code,
    name: config.name,
    description: config.description,
    icon: config.icon,
    color: config.color
  }))
}

/**
 * 创建业务卡片数据
 */
export function createAppBusinessCard(businessType: string, configs: any[]): AppBusinessCard {
  const typeConfig = getAppBusinessTypeConfig(businessType)
  if (!typeConfig) {
    throw new Error(`Unknown business type: ${businessType}`)
  }
  
  const defaultConfig = configs.find(c => c.isDefault) || configs[0]
  const startingPrice = Math.min(...configs.map(c => c.priceInfo?.memberPrice || c.priceInfo?.originalPrice || 0))
  
  return {
    businessType,
    businessName: typeConfig.name,
    description: typeConfig.description,
    icon: typeConfig.icon,
    color: typeConfig.color,
    features: typeConfig.features,
    startingPrice,
    processingDays: defaultConfig?.processingDays || typeConfig.defaultProcessingDays,
    isPopular: businessType === AppBusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE,
    isNew: businessType === AppBusinessTypeEnum.BLOCKCHAIN_EVIDENCE
  }
}

// ==================== 订单状态相关函数 ====================

/**
 * 获取订单状态名称
 */
export function getAppOrderStatusName(status: number): string {
  const config = APP_ORDER_STATUS_CONFIG[status as AppOrderStatusEnum]
  return config?.name || `状态${status}`
}

/**
 * 获取订单状态配置
 */
export function getAppOrderStatusConfig(status: number) {
  return APP_ORDER_STATUS_CONFIG[status as AppOrderStatusEnum]
}

/**
 * 获取业务状态名称
 */
export function getAppBusinessStatusName(status: string): string {
  const config = APP_BUSINESS_STATUS_CONFIG[status as AppBusinessStatusEnum]
  return config?.name || status
}

/**
 * 获取业务状态配置
 */
export function getAppBusinessStatusConfig(status: string) {
  return APP_BUSINESS_STATUS_CONFIG[status as AppBusinessStatusEnum]
}

// ==================== 价格相关函数 ====================

/**
 * 格式化价格显示（分转元）
 */
export function formatAppPrice(price: number): string {
  const { currencySymbol } = APP_BUSINESS_RULES.price
  return `${currencySymbol}${(price / 100).toFixed(2)}`
}

/**
 * 计算折扣百分比
 */
export function calculateAppDiscount(originalPrice: number, currentPrice: number): number {
  if (originalPrice <= 0) return 0
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
}

/**
 * 计算节省金额
 */
export function calculateAppSavings(originalPrice: number, currentPrice: number): number {
  return Math.max(0, originalPrice - currentPrice)
}

/**
 * 根据会员等级计算价格
 */
export function calculateAppMemberPrice(originalPrice: number, memberLevel: string): number {
  const levelConfig = APP_MEMBER_LEVEL_CONFIG[memberLevel as keyof typeof APP_MEMBER_LEVEL_CONFIG]
  if (!levelConfig) return originalPrice
  return Math.round(originalPrice * levelConfig.discountRate)
}

/**
 * 创建价格展示项列表
 */
export function createAppPriceDisplayItems(originalPrice: number): AppPriceDisplayItem[] {
  return Object.entries(APP_MEMBER_LEVEL_CONFIG).map(([level, config]) => {
    const memberPrice = calculateAppMemberPrice(originalPrice, level)
    return {
      level,
      levelName: config.name,
      price: memberPrice,
      originalPrice,
      discount: calculateAppDiscount(originalPrice, memberPrice),
      savings: calculateAppSavings(originalPrice, memberPrice),
      isRecommended: level === 'silver' // 推荐白银会员
    }
  })
}

/**
 * 创建价格卡片
 */
export function createAppPriceCard(config: any, userMemberLevel?: string): AppPriceCard {
  const originalPrice = config.priceInfo?.originalPrice || 0
  const memberPrice = userMemberLevel 
    ? calculateAppMemberPrice(originalPrice, userMemberLevel)
    : config.priceInfo?.memberPrice || originalPrice
  
  return {
    configId: config.configId,
    title: config.configName,
    subtitle: config.serviceDescription,
    originalPrice,
    currentPrice: memberPrice,
    discount: calculateAppDiscount(originalPrice, memberPrice),
    isRecommended: config.isDefault,
    isDefault: config.isDefault,
    features: config.features || [],
    processingDays: config.processingDays || 1,
    buttonText: config.isDefault ? '立即购买' : '选择此配置',
    buttonType: config.isDefault ? 'primary' : 'default'
  }
}

// ==================== 订单相关函数 ====================

/**
 * 创建订单卡片
 */
export function createAppOrderCard(order: any): AppOrderCard {
  const orderStatusConfig = getAppOrderStatusConfig(order.status)
  const businessStatusConfig = getAppBusinessStatusConfig(order.businessStatus)
  
  // 根据订单状态确定可用操作
  const actions = []
  if (order.status === AppOrderStatusEnum.UNPAID) {
    actions.push({ text: '立即支付', type: 'primary', action: 'pay' })
    actions.push({ text: '取消订单', type: 'default', action: 'cancel' })
  } else if (order.status === AppOrderStatusEnum.RECEIVED) {
    actions.push({ text: '确认收货', type: 'success', action: 'receive' })
  }
  actions.push({ text: '查看详情', type: 'default', action: 'detail' })
  
  return {
    orderId: order.id,
    orderNo: order.no,
    businessType: order.businessType || '',
    businessTypeName: getAppBusinessTypeName(order.businessType || ''),
    businessStatus: order.businessStatus || '',
    businessStatusName: getAppBusinessStatusName(order.businessStatus || ''),
    orderStatus: order.status,
    orderStatusName: orderStatusConfig?.name || '',
    totalPrice: order.totalPrice,
    payPrice: order.payPrice,
    createTime: order.createTime,
    items: (order.items || []).map((item: any) => ({
      name: item.spuName,
      image: item.picUrl,
      count: item.count,
      price: item.price
    })),
    actions: actions as any[]
  }
}

// ==================== 时间相关函数 ====================

/**
 * 格式化时间显示
 */
export function formatAppTime(time: string | Date): string {
  const date = typeof time === 'string' ? new Date(time) : time
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }
  
  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 计算预计完成时间
 */
export function calculateAppEstimatedTime(createTime: string, processingDays: number): string {
  const create = new Date(createTime)
  const estimated = new Date(create.getTime() + processingDays * 24 * 60 * 60 * 1000)
  return estimated.toISOString()
}

// ==================== 验证相关函数 ====================

/**
 * 验证价格范围
 */
export function validateAppPrice(price: number): boolean {
  const { minAmount, maxAmount } = APP_BUSINESS_RULES.price
  return price >= minAmount && price <= maxAmount
}

/**
 * 验证订单商品数量
 */
export function validateAppOrderItemCount(count: number): boolean {
  const { maxItemCount } = APP_BUSINESS_RULES.order
  return count > 0 && count <= maxItemCount
}

/**
 * 验证文件大小
 */
export function validateAppFileSize(size: number): boolean {
  const { maxFileSize } = APP_BUSINESS_RULES.upload
  return size <= maxFileSize
}

/**
 * 验证文件类型
 */
export function validateAppFileType(fileName: string): boolean {
  const { allowedTypes } = APP_BUSINESS_RULES.upload
  const extension = fileName.split('.').pop()?.toLowerCase()
  return extension ? allowedTypes.includes(extension) : false
}

// ==================== 工具函数 ====================

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

export default {
  getAppBusinessTypeName,
  getAppBusinessTypeConfig,
  getAppBusinessTypeOptions,
  createAppBusinessCard,
  getAppOrderStatusName,
  getAppOrderStatusConfig,
  getAppBusinessStatusName,
  getAppBusinessStatusConfig,
  formatAppPrice,
  calculateAppDiscount,
  calculateAppSavings,
  calculateAppMemberPrice,
  createAppPriceDisplayItems,
  createAppPriceCard,
  createAppOrderCard,
  formatAppTime,
  calculateAppEstimatedTime,
  validateAppPrice,
  validateAppOrderItemCount,
  validateAppFileSize,
  validateAppFileType,
  debounce,
  throttle
}
