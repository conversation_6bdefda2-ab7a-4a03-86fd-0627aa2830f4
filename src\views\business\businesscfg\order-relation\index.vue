<template>
  <ContentWrap>
    <!-- 搜索表单 -->
    <el-form
      ref="queryFormRef"
      :model="queryParams"
      :inline="true"
      label-width="80px"
      class="-mb-15px"
    >
        <el-form-item label="订单ID" prop="orderId">
          <el-input
            v-model="queryParams.orderId"
            placeholder="请输入订单ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            v-model="queryParams.businessType"
            placeholder="请选择业务类型"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="option in businessTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
              <div class="flex items-center">
                <Icon :icon="option.icon" class="mr-2" />
                <span>{{ option.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="业务状态" prop="businessStatus">
          <el-select
            v-model="queryParams.businessStatus"
            placeholder="请选择业务状态"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in businessStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-1" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-1" />
            重置
          </el-button>
        </el-form-item>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <!-- 操作按钮 -->
      <el-button
        type="success"
        :disabled="!selectedIds.length"
        @click="handleBatchUpdateStatus('COMPLETED')"
        v-hasPermi="['business:order-relation:update']"
      >
        <Icon icon="ep:check" class="mr-1" />
        批量完成
      </el-button>
      
      <el-button
        type="warning"
        :disabled="!selectedIds.length"
        @click="handleBatchUpdateStatus('PROCESSING')"
        v-hasPermi="['business:order-relation:update']"
      >
        <Icon icon="ep:loading" class="mr-1" />
        批量处理中
      </el-button>
      
      <el-button
        type="info"
        @click="handleExport"
        v-hasPermi="['business:order-relation:export']"
      >
        <Icon icon="ep:download" class="mr-1" />
        导出数据
      </el-button>
  </ContentWrap>

  <ContentWrap>
    <!-- 统计卡片 -->
      <el-row :gutter="16">
        <el-col :span="6" v-for="stat in statisticsData" :key="stat.status">
          <el-card class="statistics-card">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <Icon :icon="stat.icon" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
  </ContentWrap>

  <ContentWrap>
    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="orderRelationList"
      row-key="id"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      
      <el-table-column
        label="订单信息"
        width="200"
        align="center"
      >
        <template #default="{ row }">
          <div class="order-info">
            <div class="order-no">{{ row.orderInfo?.orderNo }}</div>
            <div class="order-amount">{{ formatPrice(row.orderInfo?.totalAmount || 0) }}</div>
            <el-tag
              :type="row.orderInfo?.payStatus === 'PAID' ? 'success' : 'warning'"
              size="small"
            >
              {{ row.orderInfo?.payStatus === 'PAID' ? '已支付' : '未支付' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="业务类型"
        prop="businessType"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <Icon :icon="getBusinessTypeConfig(row.businessType)?.icon" class="mr-2" />
            <span>{{ getBusinessTypeName(row.businessType) }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="配置信息"
        width="180"
        align="center"
      >
        <template #default="{ row }">
          <div class="config-info">
            <div class="config-name">{{ row.configInfo?.configName }}</div>
            <div class="processing-days">
              <Icon icon="ep:clock" class="mr-1" />
              {{ row.configInfo?.processingDays }}天
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="业务状态"
        prop="businessStatus"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getBusinessStatusConfig(row.businessStatus)?.color"
            size="small"
          >
            {{ getBusinessStatusName(row.businessStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        label="用户信息"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <div class="user-info">
            <div class="user-name">{{ row.orderInfo?.userName || '未知用户' }}</div>
            <div class="user-id">ID: {{ row.orderInfo?.userId }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="业务数据"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            v-if="row.businessData"
            type="primary"
            link
            @click="handleViewBusinessData(row)"
          >
            查看数据
          </el-button>
          <span v-else class="text-gray-400">无数据</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="创建时间"
        prop="createTime"
        width="180"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="handleViewDetail(row)"
          >
            详情
          </el-button>
          
          <el-button
            type="success"
            link
            @click="handleUpdateStatus(row)"
            v-hasPermi="['business:order-relation:update']"
          >
            更新状态
          </el-button>
          
          <el-button
            type="info"
            link
            @click="handleViewProgress(row)"
          >
            进度
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 订单详情弹窗 -->
  <OrderDetail
    ref="orderDetailRef"
  />

  <!-- 状态管理弹窗 -->
  <StatusManager
    ref="statusManagerRef"
    @success="getList"
  />

  <!-- 业务数据查看弹窗 -->
  <BusinessDataViewer
    ref="businessDataViewerRef"
  />

  <!-- 业务进度弹窗 -->
  <BusinessProgress
    ref="businessProgressRef"
  />
</template>

<script setup lang="ts" name="BusinessOrderRelationIndex">
import { BusinessOrderRelationApi } from '@/api/business/businesscfg/order-relation'
import { BusinessOrderRelationTableItem, BusinessOrderRelationQueryForm } from '@/types/business/businesscfg/order-relation'
import {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getBusinessStatusName,
  getBusinessStatusConfig,
  getBusinessTypeOptions,
  getBusinessStatusOptions,
  formatPrice
} from '@/utils/business/businesscfg/helpers'
import { formatDate } from '@/utils/formatTime'
import OrderDetail from './components/OrderDetail.vue'
import StatusManager from './components/StatusManager.vue'
import BusinessDataViewer from './components/BusinessDataViewer.vue'
import BusinessProgress from './components/BusinessProgress.vue'

// 响应式数据
const loading = ref(false)
const orderRelationList = ref<BusinessOrderRelationTableItem[]>([])
const total = ref(0)
const selectedIds = ref<number[]>([])

// 查询参数
const queryParams = reactive<BusinessOrderRelationQueryForm & { pageNo: number; pageSize: number }>({
  pageNo: 1,
  pageSize: 20,
  orderId: undefined,
  businessType: '',
  businessStatus: '',
  createTime: undefined
})

// 统计数据
const statisticsData = ref([
  { status: 'PENDING', label: '待处理', count: 0, color: '#E6A23C', icon: 'ep:clock' },
  { status: 'PROCESSING', label: '处理中', count: 0, color: '#409EFF', icon: 'ep:loading' },
  { status: 'COMPLETED', label: '已完成', count: 0, color: '#67C23A', icon: 'ep:check' },
  { status: 'FAILED', label: '处理失败', count: 0, color: '#F56C6C', icon: 'ep:close' }
])

// 表单引用
const queryFormRef = ref()
const tableRef = ref()
const orderDetailRef = ref()
const statusManagerRef = ref()
const businessDataViewerRef = ref()
const businessProgressRef = ref()

// 选项数据
const businessTypeOptions = getBusinessTypeOptions()
const businessStatusOptions = getBusinessStatusOptions()

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await BusinessOrderRelationApi.getOrderRelationPage(queryParams)
    orderRelationList.value = data.list || []
    total.value = data.total || 0
    
    // 更新统计数据
    updateStatistics()
  } catch (error) {
    console.error('获取订单关联列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStatistics = () => {
  const statusCounts = orderRelationList.value.reduce((acc, item) => {
    acc[item.businessStatus] = (acc[item.businessStatus] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  statisticsData.value.forEach(stat => {
    stat.count = statusCounts[stat.status] || 0
  })
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

// 查看详情
const handleViewDetail = (row: BusinessOrderRelationTableItem) => {
  orderDetailRef.value?.open(row)
}

// 更新状态
const handleUpdateStatus = (row: BusinessOrderRelationTableItem) => {
  statusManagerRef.value?.open(row)
}

// 查看业务数据
const handleViewBusinessData = (row: BusinessOrderRelationTableItem) => {
  businessDataViewerRef.value?.open(row)
}

// 查看进度
const handleViewProgress = (row: BusinessOrderRelationTableItem) => {
  businessProgressRef.value?.open(row)
}

// 选择变化
const handleSelectionChange = (selection: BusinessOrderRelationTableItem[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 排序变化
const handleSortChange = ({ prop, order }: any) => {
  // 处理排序逻辑
  getList()
}

// 批量更新状态
const handleBatchUpdateStatus = async (status: string) => {
  const statusName = getBusinessStatusName(status)
  await ElMessageBox.confirm(`确定要将选中的业务状态更新为"${statusName}"吗？`, '提示', {
    type: 'warning'
  })
  
  try {
    await BusinessOrderRelationApi.batchUpdateStatus(selectedIds.value, status)
    ElMessage.success('更新成功')
    getList()
  } catch (error) {
    console.error('批量更新状态失败:', error)
  }
}

// 导出数据
const handleExport = async () => {
  try {
    await BusinessOrderRelationApi.exportOrderRelations(queryParams)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.statistics-card {
  margin-bottom: 16px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.order-info {
  text-align: left;
}

.order-no {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.order-amount {
  font-weight: 600;
  color: #e74c3c;
  margin-bottom: 4px;
}

.config-info {
  text-align: left;
}

.config-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.processing-days {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.user-info {
  text-align: left;
}

.user-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.user-id {
  font-size: 12px;
  color: #666;
}
</style>
