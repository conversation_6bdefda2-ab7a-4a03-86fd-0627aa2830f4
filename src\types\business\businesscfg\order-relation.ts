// 业务状态枚举
export enum BusinessStatusEnum {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// 业务状态显示名称映射
export const BusinessStatusNames: Record<BusinessStatusEnum, string> = {
  [BusinessStatusEnum.PENDING]: '待处理',
  [BusinessStatusEnum.PROCESSING]: '处理中',
  [BusinessStatusEnum.COMPLETED]: '已完成',
  [BusinessStatusEnum.FAILED]: '处理失败',
  [BusinessStatusEnum.CANCELLED]: '已取消'
}

// 业务状态颜色映射
export const BusinessStatusColors: Record<BusinessStatusEnum, string> = {
  [BusinessStatusEnum.PENDING]: 'warning',
  [BusinessStatusEnum.PROCESSING]: 'primary',
  [BusinessStatusEnum.COMPLETED]: 'success',
  [BusinessStatusEnum.FAILED]: 'danger',
  [BusinessStatusEnum.CANCELLED]: 'info'
}

// 业务订单关联表格项
export interface BusinessOrderRelationTableItem {
  id: number
  orderId: number
  businessType: string
  businessTypeName: string
  businessId?: number
  businessProductConfigId: number
  businessStatus: string
  businessStatusName: string
  businessData?: string
  processResult?: string
  createTime: string
  updateTime: string
  // 关联信息
  orderInfo?: {
    orderNo: string
    userId: number
    userName?: string
    totalAmount: number
    payStatus: string
    payTime?: string
  }
  configInfo?: {
    configName: string
    processingDays: number
    serviceDescription?: string
  }
}

// 业务订单关联查询表单
export interface BusinessOrderRelationQueryForm {
  orderId?: number
  businessType?: string
  businessStatus?: string
  businessProductConfigId?: number
  createTime?: string[]
}

// 业务订单关联更新表单
export interface BusinessOrderRelationUpdateForm {
  id: number
  businessStatus: string
  processResult?: string
  businessData?: string
}

// 业务进度数据
export interface BusinessProgressData {
  id: number
  orderId: number
  businessType: string
  currentStatus: string
  statusHistory: {
    status: string
    statusName: string
    time: string
    operator?: string
    remark?: string
  }[]
  estimatedCompletionTime?: string
  actualCompletionTime?: string
}

// 业务数据展示项
export interface BusinessDataItem {
  key: string
  label: string
  value: any
  type: 'text' | 'number' | 'date' | 'json' | 'file' | 'image'
  format?: string
}

// 状态管理操作
export interface StatusManagerAction {
  status: string
  statusName: string
  color: string
  icon: string
  description: string
  requiresResult: boolean
  allowedFromStatuses: string[]
}

// 批量操作数据
export interface BatchOperationData {
  selectedIds: number[]
  operation: 'updateStatus' | 'export' | 'delete'
  params?: any
}

// 统计数据
export interface BusinessStatisticsData {
  totalCount: number
  statusCounts: Record<string, number>
  typeDistribution: {
    businessType: string
    businessTypeName: string
    count: number
    percentage: number
  }[]
  dailyTrends: {
    date: string
    count: number
    completedCount: number
  }[]
}

export default {
  BusinessStatusEnum,
  BusinessStatusNames,
  BusinessStatusColors
}
