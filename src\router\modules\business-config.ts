import { Layout } from '@/utils/routerHelper'
import { AppRouteRecordRaw } from '@/types/router'

const businessConfigRouter: AppRouteRecordRaw = {
  path: '/business-config',
  component: Layout,
  redirect: '/business-config/index',
  name: 'BusinessConfig',
  meta: {
    title: '业务配置管理',
    icon: 'ep:setting',
    alwaysShow: true
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/business/businesscfg/config/index.vue'),
      name: 'BusinessConfigIndex',
      meta: {
        title: '配置管理',
        icon: 'ep:list',
        noCache: false,
        permission: ['business:product-config:query']
      }
    },
    {
      path: 'order-relation',
      component: () => import('@/views/business/businesscfg/config/order-relation/index.vue'),
      name: 'BusinessOrderRelation',
      meta: {
        title: '订单关联',
        icon: 'ep:connection',
        noCache: false,
        permission: ['business:order-relation:query']
      }
    },
    {
      path: 'price-matrix',
      component: () => import('@/views/business/businesscfg/config/price-matrix.vue'),
      name: 'BusinessPriceMatrix',
      meta: {
        title: '价格矩阵',
        icon: 'ep:grid',
        noCache: false,
        permission: ['business:product-config:query']
      }
    },
    {
      path: 'statistics',
      component: () => import('@/views/business/businesscfg/config/statistics.vue'),
      name: 'BusinessStatistics',
      meta: {
        title: '数据统计',
        icon: 'ep:data-analysis',
        noCache: false,
        permission: ['business:product-config:query']
      }
    }
  ]
}

export default businessConfigRouter
