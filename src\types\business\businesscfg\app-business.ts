// ==================== 业务类型枚举 ====================

export enum AppBusinessTypeEnum {
  DATA_CERTIFICATE_EVIDENCE = 'data_certificate_evidence',
  SOFTWARE_REGISTER = 'software_register',
  REAL_IDENTIFY = 'real_identify',
  COPYRIGHT_REGISTER = 'copyright_register',
  BLOCKCHAIN_EVIDENCE = 'blockchain_evidence'
}

export enum AppBusinessStatusEnum {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export enum AppOrderStatusEnum {
  UNPAID = 0,      // 待支付
  PAID = 10,       // 已支付
  DELIVERED = 20,  // 已发货
  RECEIVED = 30,   // 已收货
  COMPLETED = 40,  // 已完成
  CANCELLED = 50   // 已取消
}

// ==================== 业务配置相关类型 ====================

// 业务类型配置
export interface AppBusinessTypeConfig {
  code: string
  name: string
  description: string
  icon: string
  color: string
  features: string[]
  defaultProcessingDays: number
}

// 价格展示项
export interface AppPriceDisplayItem {
  level: string
  levelName: string
  price: number
  originalPrice: number
  discount: number
  savings: number
  isRecommended?: boolean
}

// 服务特性项
export interface AppServiceFeature {
  icon: string
  title: string
  description: string
}

// 服务流程步骤
export interface AppServiceStep {
  step: number
  title: string
  description: string
  estimatedTime?: string
}

// 配置对比项
export interface AppConfigComparison {
  configId: number
  configName: string
  isDefault: boolean
  isRecommended?: boolean
  processingDays: number
  price: number
  memberPrice?: number
  features: string[]
  advantages: string[]
}

// ==================== 订单相关类型 ====================

// 订单状态配置
export interface AppOrderStatusConfig {
  status: number
  name: string
  color: string
  icon: string
  description: string
}

// 业务状态配置
export interface AppBusinessStatusConfig {
  status: string
  name: string
  color: string
  icon: string
  description: string
}

// 订单筛选条件
export interface AppOrderFilterOptions {
  status?: number
  businessType?: string
  businessStatus?: string
  timeRange?: string[]
}

// 订单统计数据
export interface AppOrderStatistics {
  totalCount: number
  statusCounts: Record<number, number>
  businessStatusCounts: Record<string, number>
  totalAmount: number
  paidAmount: number
}

// ==================== 业务进度相关类型 ====================

// 进度状态项
export interface AppProgressStatusItem {
  status: string
  statusName: string
  time: string
  description?: string
  operator?: string
  isCompleted: boolean
  isCurrent: boolean
}

// 业务进度信息
export interface AppBusinessProgressInfo {
  orderId: number
  businessType: string
  businessTypeName: string
  currentStatus: string
  currentStatusName: string
  progressPercentage: number
  estimatedCompletionTime?: string
  actualCompletionTime?: string
  statusHistory: AppProgressStatusItem[]
  nextSteps?: string[]
}

// 进度时间线项
export interface AppProgressTimelineItem {
  time: string
  title: string
  description?: string
  status: 'completed' | 'current' | 'pending' | 'failed'
  icon: string
  color: string
}

// ==================== 用户界面相关类型 ====================

// 卡片展示项
export interface AppBusinessCard {
  businessType: string
  businessName: string
  description: string
  icon: string
  color: string
  features: string[]
  startingPrice: number
  processingDays: number
  isPopular?: boolean
  isNew?: boolean
}

// 价格卡片
export interface AppPriceCard {
  configId: number
  title: string
  subtitle?: string
  originalPrice: number
  currentPrice: number
  discount?: number
  isRecommended?: boolean
  isDefault?: boolean
  features: string[]
  processingDays: number
  buttonText: string
  buttonType: 'primary' | 'default' | 'success'
}

// 订单卡片
export interface AppOrderCard {
  orderId: number
  orderNo: string
  businessType: string
  businessTypeName: string
  businessStatus: string
  businessStatusName: string
  orderStatus: number
  orderStatusName: string
  totalPrice: number
  payPrice: number
  createTime: string
  items: Array<{
    name: string
    image?: string
    count: number
    price: number
  }>
  actions: Array<{
    text: string
    type: 'primary' | 'default' | 'success' | 'warning' | 'danger'
    action: string
  }>
}

// ==================== 表单相关类型 ====================

// 业务申请表单
export interface AppBusinessApplicationForm {
  businessType: string
  configId: number
  businessData?: Record<string, any>
  remark?: string
}

// 订单创建表单
export interface AppOrderCreateForm {
  items: Array<{
    skuId: number
    count: number
  }>
  addressId?: number
  remark?: string
  businessType?: string
  businessData?: Record<string, any>
}

// ==================== 响应数据类型 ====================

// 通用响应结果
export interface AppApiResponse<T = any> {
  code: number
  data: T
  msg: string
}

// 分页响应结果
export interface AppPageResult<T> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
}

// 操作结果
export interface AppOperationResult {
  success: boolean
  message: string
  data?: any
}

export default {
  AppBusinessTypeEnum,
  AppBusinessStatusEnum,
  AppOrderStatusEnum
}
