<template>
  <Dialog
    v-model="dialogVisible"
    title="业务数据查看"
    width="800px"
    @close="handleClose"
  >
    <div v-if="orderData" class="business-data-viewer">
      <!-- 基本信息 -->
      <div class="data-header">
        <div class="header-item">
          <span class="label">订单编号：</span>
          <span class="value">{{ orderData.orderInfo?.orderNo }}</span>
        </div>
        <div class="header-item">
          <span class="label">业务类型：</span>
          <div class="value">
            <Icon :icon="getBusinessTypeConfig(orderData.businessType)?.icon" class="mr-2" />
            {{ getBusinessTypeName(orderData.businessType) }}
          </div>
        </div>
        <div class="header-item">
          <span class="label">业务状态：</span>
          <el-tag
            :type="getBusinessStatusConfig(orderData.businessStatus)?.color"
            size="small"
          >
            {{ getBusinessStatusName(orderData.businessStatus) }}
          </el-tag>
        </div>
      </div>

      <!-- 数据展示 -->
      <div class="data-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 结构化数据 -->
          <el-tab-pane label="结构化数据" name="structured">
            <div v-if="structuredData.length" class="structured-data">
              <el-table :data="structuredData" border>
                <el-table-column label="字段名称" prop="label" width="200" />
                <el-table-column label="字段类型" prop="type" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getTypeColor(row.type)" size="small">
                      {{ getTypeName(row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="字段值" prop="value">
                  <template #default="{ row }">
                    <div v-if="row.type === 'json'" class="json-field">
                      <el-button type="primary" link @click="viewJsonData(row.value)">
                        查看JSON
                      </el-button>
                    </div>
                    <div v-else-if="row.type === 'image'" class="image-field">
                      <el-image
                        :src="row.value"
                        :preview-src-list="[row.value]"
                        style="width: 80px; height: 80px"
                        fit="cover"
                      />
                    </div>
                    <div v-else-if="row.type === 'file'" class="file-field">
                      <el-button type="primary" link @click="downloadFile(row.value)">
                        <Icon icon="ep:download" class="mr-1" />
                        下载文件
                      </el-button>
                    </div>
                    <div v-else-if="row.type === 'date'" class="date-field">
                      {{ formatDate(row.value) }}
                    </div>
                    <div v-else class="text-field">
                      <el-input
                        :model-value="String(row.value)"
                        readonly
                        :type="String(row.value).length > 50 ? 'textarea' : 'text'"
                        :rows="3"
                      />
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无结构化数据" />
          </el-tab-pane>

          <!-- 原始数据 -->
          <el-tab-pane label="原始数据" name="raw">
            <div class="raw-data">
              <div class="data-actions">
                <el-button type="primary" @click="formatJson">
                  <Icon icon="ep:magic-stick" class="mr-1" />
                  格式化JSON
                </el-button>
                <el-button @click="copyData">
                  <Icon icon="ep:copy-document" class="mr-1" />
                  复制数据
                </el-button>
                <el-button @click="downloadData">
                  <Icon icon="ep:download" class="mr-1" />
                  下载数据
                </el-button>
              </div>
              <el-input
                v-model="rawDataText"
                type="textarea"
                :rows="20"
                readonly
                placeholder="暂无原始数据"
                class="raw-data-input"
              />
            </div>
          </el-tab-pane>

          <!-- 数据统计 -->
          <el-tab-pane label="数据统计" name="statistics">
            <div class="data-statistics">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-statistic title="字段总数" :value="dataStatistics.fieldCount" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="数据大小" :value="dataStatistics.dataSize" suffix="字节" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="创建时间" :value="orderData.createTime" formatter="date" />
                </el-col>
              </el-row>
              
              <div class="field-types">
                <h4>字段类型分布</h4>
                <div class="type-chart">
                  <div
                    v-for="(count, type) in dataStatistics.typeDistribution"
                    :key="type"
                    class="type-item"
                  >
                    <el-tag :type="getTypeColor(type)" size="small">
                      {{ getTypeName(type) }}
                    </el-tag>
                    <span class="type-count">{{ count }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExport">
        <Icon icon="ep:download" class="mr-1" />
        导出数据
      </el-button>
    </template>

    <!-- JSON查看弹窗 -->
    <Dialog
      v-model="jsonDialogVisible"
      title="JSON数据详情"
      width="700px"
    >
      <el-input
        :model-value="jsonViewData"
        type="textarea"
        :rows="20"
        readonly
      />
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts" name="BusinessDataViewer">
import { BusinessOrderRelationTableItem } from '@/types/business/businesscfg/order-relation'
import { BusinessDataItem } from '@/types/business/businesscfg/order-relation'
import {
  getBusinessTypeName,
  getBusinessTypeConfig,
  getBusinessStatusName,
  getBusinessStatusConfig,
  formatBusinessData
} from '@/utils/business/businesscfg/helpers'
import { formatDate } from '@/utils/formatTime'

// 响应式数据
const dialogVisible = ref(false)
const jsonDialogVisible = ref(false)
const activeTab = ref('structured')
const orderData = ref<BusinessOrderRelationTableItem | null>(null)
const structuredData = ref<BusinessDataItem[]>([])
const rawDataText = ref('')
const jsonViewData = ref('')

// 数据统计
const dataStatistics = computed(() => {
  const fieldCount = structuredData.value.length
  const dataSize = rawDataText.value.length
  const typeDistribution = structuredData.value.reduce((acc, item) => {
    acc[item.type] = (acc[item.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return {
    fieldCount,
    dataSize,
    typeDistribution
  }
})

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    text: '',
    number: 'success',
    date: 'warning',
    json: 'primary',
    file: 'info',
    image: 'danger'
  }
  return colorMap[type] || ''
}

// 获取类型名称
const getTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    text: '文本',
    number: '数字',
    date: '日期',
    json: 'JSON',
    file: '文件',
    image: '图片'
  }
  return nameMap[type] || type
}

// 打开弹窗
const open = (data: BusinessOrderRelationTableItem) => {
  orderData.value = data
  dialogVisible.value = true
  activeTab.value = 'structured'
  
  // 解析业务数据
  parseBusinessData(data.businessData)
}

// 解析业务数据
const parseBusinessData = (data?: string) => {
  if (!data) {
    structuredData.value = []
    rawDataText.value = ''
    return
  }
  
  rawDataText.value = data
  structuredData.value = formatBusinessData(data)
}

// 查看JSON数据
const viewJsonData = (data: any) => {
  jsonViewData.value = JSON.stringify(data, null, 2)
  jsonDialogVisible.value = true
}

// 下载文件
const downloadFile = (url: string) => {
  window.open(url, '_blank')
}

// 格式化JSON
const formatJson = () => {
  try {
    const parsed = JSON.parse(rawDataText.value)
    rawDataText.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('数据不是有效的JSON格式')
  }
}

// 复制数据
const copyData = async () => {
  try {
    await navigator.clipboard.writeText(rawDataText.value)
    ElMessage.success('数据已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载数据
const downloadData = () => {
  const blob = new Blob([rawDataText.value], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `business-data-${orderData.value?.id}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 导出数据
const handleExport = () => {
  if (!orderData.value) return
  
  const exportData = {
    orderInfo: {
      orderNo: orderData.value.orderInfo?.orderNo,
      businessType: getBusinessTypeName(orderData.value.businessType),
      businessStatus: getBusinessStatusName(orderData.value.businessStatus),
      createTime: orderData.value.createTime
    },
    structuredData: structuredData.value,
    rawData: rawDataText.value,
    statistics: dataStatistics.value
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `business-data-export-${orderData.value.id}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('数据导出成功')
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  orderData.value = null
  structuredData.value = []
  rawDataText.value = ''
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.business-data-viewer {
  padding: 20px;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.header-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
}

.value {
  color: #333;
  display: flex;
  align-items: center;
}

.data-content {
  min-height: 400px;
}

.structured-data {
  padding: 16px;
}

.json-field,
.image-field,
.file-field,
.date-field,
.text-field {
  padding: 4px 0;
}

.raw-data {
  padding: 16px;
}

.data-actions {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.raw-data-input {
  font-family: 'Courier New', monospace;
}

.data-statistics {
  padding: 16px;
}

.field-types {
  margin-top: 24px;
}

.field-types h4 {
  margin-bottom: 16px;
  color: #333;
}

.type-chart {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
}

.type-count {
  font-weight: 600;
  color: #333;
}
</style>
