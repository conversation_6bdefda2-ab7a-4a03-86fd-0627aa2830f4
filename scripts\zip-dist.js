const fs = require('fs');
const archiver = require('archiver');
const path = require('path');

// 获取当前日期和时间
const now = new Date();
const dateStr = now.getFullYear().toString() +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0');

// 添加时间戳
const timeStr = now.getHours().toString().padStart(2, '0') +
    now.getMinutes().toString().padStart(2, '0') +
    now.getSeconds().toString().padStart(2, '0') +
    now.getMilliseconds().toString().padStart(3, '0');

// 组合日期时间戳
const dateTimeStr = `${dateStr}.${timeStr}`;

// 创建输出流
const output = fs.createWriteStream(path.join(__dirname, `../dist.rel.${dateTimeStr}.zip`));
const archive = archiver('zip', {
    zlib: { level: 9 } // 设置最大压缩级别
});

// 监听所有存档数据都已被写入底层流
output.on('close', () => {
    console.log(`Archive created successfully: ${archive.pointer()} total bytes`);
    console.log(`File saved as: dist.rel.${dateTimeStr}.zip`);
});

// 监听警告
archive.on('warning', (err) => {
    if (err.code === 'ENOENT') {
        console.warn(err);
    } else {
        throw err;
    }
});

// 监听错误
archive.on('error', (err) => {
    throw err;
});

// 将输出流管道连接到存档
archive.pipe(output);

// 添加 dist-prod 目录到压缩文件，将第二个参数设为 'dist' 保留目录本身
archive.directory('dist-prod/', 'dist');

// 完成归档
archive.finalize(); 