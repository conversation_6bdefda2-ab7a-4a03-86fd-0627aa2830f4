<template>
  <el-dialog
    v-model="dialogVisible"
    title="存证证书"
    width="800px"
    :before-close="handleClose"
    append-to-body
  >
    <div class="certificate-container" ref="certificateRef">
      <div class="certificate-content">
        <!-- 证书背景图片 -->
        <div class="certificate-background">
          <img 
            src="@/assets/imgs/certification/deposit_certificate_new.png" 
            alt="存证证书"
            class="certificate-image"
          />
          
          <!-- 证书信息覆盖层 -->
          <div class="certificate-overlay">
            <!-- 数据编号 -->
            <div class="field-item data-number">
              {{ certificateData?.dataNumber || '' }}
            </div>
            
            <!-- 数据名称 -->
            <div class="field-item data-name">
              {{ certificateData?.dataName || '' }}
            </div>
            
            <!-- 存证单位 -->
            <div class="field-item certification-unit">
              {{ certificateData?.certificationUnit || '' }}
            </div>
            
            <!-- 数据分类 -->
            <div class="field-item data-classification">
              {{ getDataClassificationLabel(certificateData?.dataClassification) }}
            </div>
            
            <!-- 数据级别 -->
            <div class="field-item data-level">
              {{ getDataLevelLabel(certificateData?.dataLevel) }}
            </div>
            
            <!-- 数据生成时间 -->
            <div class="field-item data-generation-time">
              {{ formatDate(certificateData?.dataGenerationTime) }}
            </div>
            
            <!-- 数据来源信息 -->
            <div class="field-item data-source-info">
              {{ getDataSourceInfoLabel(certificateData?.dataSourceInfo) }}
            </div>
            
            <!-- 创建时间 -->
            <div class="field-item create-time">
              {{ formatDate(certificateData?.createTime) }}
            </div>
            
            <!-- 数据块哈希值 -->
            <div class="field-item hash-value">
              {{ certificateData?.dataBlockHashValue || '' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="downloadPDF" :loading="downloadLoading">
          <Icon icon="ep:download" class="mr-5px" />
          下载证书
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, withDefaults, defineProps, defineEmits, defineExpose } from 'vue'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { CertificateInfoVO } from '@/api/business/certificateinfo'
import { ElMessage } from 'element-plus'

// 定义组件属性
interface Props {
  modelValue: boolean
  certificateData?: CertificateInfoVO
}

// 定义事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  certificateData: undefined
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const certificateRef = ref<HTMLElement>()
const downloadLoading = ref(false)

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
})

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取字典标签的辅助函数
const getDataClassificationLabel = (value?: string) => {
  if (!value) return ''
  const options = getStrDictOptions(DICT_TYPE.DATA_CLASSIFICATION)
  const option = options.find(item => item.value === value)
  return option?.label || value
}

const getDataLevelLabel = (value?: string) => {
  if (!value) return ''
  const options = getStrDictOptions(DICT_TYPE.DATA_LEVEL)
  const option = options.find(item => item.value === value)
  return option?.label || value
}

const getDataSourceInfoLabel = (value?: string) => {
  if (!value) return ''
  const options = getStrDictOptions(DICT_TYPE.DATA_SOURCE_INFO)
  const option = options.find(item => item.value === value)
  return option?.label || value
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 下载PDF功能
const downloadPDF = async () => {
  if (!certificateRef.value || !props.certificateData) return

  downloadLoading.value = true

  try {
    // 动态导入PDF相关库
    const [{ default: html2canvas }, { default: jsPDF }] = await Promise.all([
      import('html2canvas'),
      import('jspdf')
    ])

    // 等待DOM更新
    await nextTick()

    // 生成canvas
    const canvas = await html2canvas(certificateRef.value, {
      scale: 3, // 提高清晰度
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: certificateRef.value.offsetWidth,
      height: certificateRef.value.offsetHeight
    })

    // 创建PDF
    const imgData = canvas.toDataURL('image/png', 1.0)
    const pdf = new jsPDF({
      orientation: 'landscape', // 横向
      unit: 'mm',
      format: 'a4'
    })

    // 计算图片在PDF中的尺寸
    const pdfWidth = pdf.internal.pageSize.getWidth()
    const pdfHeight = pdf.internal.pageSize.getHeight()
    const imgWidth = canvas.width
    const imgHeight = canvas.height

    // 按比例缩放，确保图片完全显示在PDF中
    const ratio = Math.min(pdfWidth / (imgWidth * 0.264583), pdfHeight / (imgHeight * 0.264583))
    const scaledWidth = (imgWidth * 0.264583) * ratio
    const scaledHeight = (imgHeight * 0.264583) * ratio

    // 居中显示
    const imgX = (pdfWidth - scaledWidth) / 2
    const imgY = (pdfHeight - scaledHeight) / 2

    pdf.addImage(imgData, 'PNG', imgX, imgY, scaledWidth, scaledHeight)

    // 下载PDF
    const fileName = `存证证书_${props.certificateData.dataNumber}_${formatDate(new Date(), 'YYYY-MM-DD')}.pdf`
    pdf.save(fileName)

    ElMessage.success('证书下载成功')

  } catch (error) {
    console.error('PDF生成失败:', error)
    ElMessage.error('PDF生成失败，请稍后重试')
  } finally {
    downloadLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open: () => {
    dialogVisible.value = true
  },
  close: () => {
    dialogVisible.value = false
  }
})
</script>

<style scoped lang="scss">
.certificate-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background-color: #f8f9fa;
  padding: 20px;
}

.certificate-content {
  position: relative;
  max-width: 100%;
  max-height: 75vh;
  overflow: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: white;
}

.certificate-background {
  position: relative;
  display: inline-block;
  width: 100%;
}

.certificate-image {
  width: 100%;
  height: auto;
  display: block;
  max-width: 800px;
}

.certificate-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.field-item {
  position: absolute;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* 根据证书模板调整各字段的位置 - 需要根据实际模板调整 */
.data-number {
  top: 25%;
  left: 25%;
  width: 50%;
  font-size: 18px;
  font-weight: 700;
  color: #1a365d;
}

.data-name {
  top: 35%;
  left: 20%;
  width: 60%;
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
}

.certification-unit {
  top: 45%;
  left: 25%;
  width: 50%;
  font-size: 16px;
  color: #4a5568;
}

.data-classification {
  top: 55%;
  left: 15%;
  width: 30%;
  font-size: 14px;
  color: #4a5568;
}

.data-level {
  top: 55%;
  right: 15%;
  width: 30%;
  font-size: 14px;
  color: #4a5568;
}

.data-generation-time {
  top: 65%;
  left: 25%;
  width: 50%;
  font-size: 14px;
  color: #4a5568;
}

.data-source-info {
  top: 75%;
  left: 25%;
  width: 50%;
  font-size: 14px;
  color: #4a5568;
}

.create-time {
  top: 85%;
  left: 25%;
  width: 50%;
  font-size: 14px;
  color: #4a5568;
}

.hash-value {
  bottom: 8%;
  left: 5%;
  right: 5%;
  font-size: 10px;
  font-family: 'Courier New', monospace;
  color: #718096;
  text-align: left;
  line-height: 1.2;
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
