<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="数据编号" prop="dataNumber">
        <el-input
          v-model="queryParams.dataNumber"
          placeholder="请输入数据编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据名称" prop="dataName">
        <el-input
          v-model="queryParams.dataName"
          placeholder="请输入数据名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="存证单位" prop="certificationUnit">
        <el-input
          v-model="queryParams.certificationUnit"
          placeholder="请输入存证单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据分类" prop="dataClassification">
        <el-select
          v-model="queryParams.dataClassification"
          placeholder="请选择数据分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_CLASSIFICATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据级别" prop="dataLevel">
        <el-select
          v-model="queryParams.dataLevel"
          placeholder="请选择数据级别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生成时间" prop="dataGenerationTime">
        <el-date-picker
          v-model="queryParams.dataGenerationTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <!-- <el-form-item label="数据规模" prop="dataScale">
        <el-input
          v-model="queryParams.dataScale"
          placeholder="请输入数据规模"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据规模单位" prop="dataScaleUnit">
        <el-select
          v-model="queryParams.dataScaleUnit"
          placeholder="请选择数据规模单位"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_SCALE_UNIT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="数据资源形态" prop="dataResourceForm">
        <el-select
          v-model="queryParams.dataResourceForm"
          placeholder="请选择数据资源形态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_RESOURCE_FORM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="数据块哈希值" prop="dataBlockHashValue">
        <el-input
          v-model="queryParams.dataBlockHashValue"
          placeholder="请输入数据块哈希值"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据访问地址" prop="dataAccessAddress">
        <el-input
          v-model="queryParams.dataAccessAddress"
          placeholder="请输入数据访问地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="扩展信息哈希值" prop="extendedInfoHashValue">
        <el-input
          v-model="queryParams.extendedInfoHashValue"
          placeholder="请输入扩展信息哈希值"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="扩展信息访问地址" prop="extendedInfoAccessAddress">
        <el-input
          v-model="queryParams.extendedInfoAccessAddress"
          placeholder="请输入扩展信息访问地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="数据来源" prop="dataSourceInfo">
        <el-select
          v-model="queryParams.dataSourceInfo"
          placeholder="请选择数据来源"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_SOURCE_INFO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="数据来源具体信息" prop="dataSourceSpecificInfo">
        <el-input
          v-model="queryParams.dataSourceSpecificInfo"
          placeholder="请输入数据来源具体信息"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="审核状态" prop="statusCd">
        <el-select
          v-model="queryParams.statusCd"
          placeholder="请选择审核状态"
          multiple
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['business:certificate-info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['business:certificate-info:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="数据编号" align="center" prop="dataNumber" />
      <el-table-column label="数据名称" align="center" prop="dataName" />
      <el-table-column label="存证单位" align="center" prop="certificationUnit" />
      <el-table-column label="数据分类" align="center" prop="dataClassification">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_CLASSIFICATION" :value="scope.row.dataClassification" />
        </template>
      </el-table-column>
      <el-table-column label="数据级别" align="center" prop="dataLevel">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_LEVEL" :value="scope.row.dataLevel" />
        </template>
      </el-table-column>
      <el-table-column
        label="数据生成时间"
        align="center"
        prop="dataGenerationTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <!-- <el-table-column label="数据规模" align="center" prop="dataScale" />
      <el-table-column label="数据规模单位" align="center" prop="dataScaleUnit">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_SCALE_UNIT" :value="scope.row.dataScaleUnit" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="数据资源形态" align="center" prop="dataResourceForm">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_RESOURCE_FORM" :value="scope.row.dataResourceForm" />
        </template>
      </el-table-column>
      <el-table-column label="数据资源权属申明" align="center" prop="dataResourceOwnership">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_RESOURCE_OWNERSHIP" :value="scope.row.dataResourceOwnership" />
        </template>
      </el-table-column>
      <el-table-column label="数据样本文件路径" align="center" prop="dataSampleFile" />
      <el-table-column label="限制情况说明" align="center" prop="restrictionDescription" />
      <el-table-column label="其他描述" align="center" prop="otherDescription" />
      <el-table-column label="数据块哈希值" align="center" prop="dataBlockHashValue" />
      <el-table-column label="数据访问地址" align="center" prop="dataAccessAddress" />
      <el-table-column label="扩展信息哈希值" align="center" prop="extendedInfoHashValue" />
      <el-table-column label="扩展信息访问地址" align="center" prop="extendedInfoAccessAddress" /> -->
      <el-table-column label="数据来源信息" align="center" prop="dataSourceInfo">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_SOURCE_INFO" :value="scope.row.dataSourceInfo" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="数据来源具体信息" align="center" prop="dataSourceSpecificInfo" />
      <el-table-column label="数据来源佐证材料路径" align="center" prop="dataSourceEvidenceMaterial" />
      <el-table-column label="有效控制措施佐证材料路径" align="center" prop="effectiveControlEvidenceMaterial" />
      <el-table-column label="个人信息采集的合法佐证材料路径" align="center" prop="personalInfoCollectionEvidenceMaterial" /> -->
      <el-table-column label="审核状态" align="center" prop="statusCd">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE" :value="getLatestReviewStatus(scope.row.certificateReviews)" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="success"
            @click="openCertificate(scope.row)"
            v-hasPermi="['business:certificate-info:view']"
          >
            <Icon icon="ep:document" class="mr-5px" />
            查看证书
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['business:certificate-info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['business:certificate-info:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CertificateInfoForm ref="formRef" @success="getList" />

  <!-- 证书弹窗 -->
  <CertificateDialog v-model="certificateDialogVisible" :certificate-data="currentCertificateData" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CertificateInfoApi, CertificateInfoVO, CertificateReviewVO } from '@/api/business/certificateinfo'
import CertificateInfoForm from './CertificateInfoForm.vue'
import CertificateDialog from './CertificateDialog.vue'

/** 存证信息表，用于记录各类数据存证相关信息 列表 */
defineOptions({ name: 'CertificateInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CertificateInfoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dataNumber: undefined,
  dataName: undefined,
  certificationUnit: undefined,
  dataClassification: undefined,
  dataLevel: undefined,
  dataGenerationTime: [],
  dataScale: undefined,
  dataScaleUnit: undefined,
  dataResourceForm: undefined,
  dataResourceOwnership: undefined,
  dataSampleFile: undefined,
  restrictionDescription: undefined,
  otherDescription: undefined,
  dataBlockHashValue: undefined,
  dataAccessAddress: undefined,
  extendedInfoHashValue: undefined,
  extendedInfoAccessAddress: undefined,
  dataSourceInfo: undefined,
  dataSourceSpecificInfo: undefined,
  dataSourceEvidenceMaterial: undefined,
  effectiveControlEvidenceMaterial: undefined,
  personalInfoCollectionEvidenceMaterial: undefined,
  statusCd: [] as string[], // 审核状态（支持多选，初始化时会设置为所有状态）
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 证书弹窗相关
const certificateDialogVisible = ref(false) // 证书弹窗显示状态
const currentCertificateData = ref<CertificateInfoVO>() // 当前查看的证书数据

/** 获取最新的审核状态 */
const getLatestReviewStatus = (certificateReviews: CertificateReviewVO[]): string => {
  if (!certificateReviews || certificateReviews.length === 0) {
    return '' // 如果没有审核记录，返回空字符串
  }

  // 按创建时间降序排序，获取最新的审核记录
  const sortedReviews = certificateReviews.sort((a, b) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  })

  return sortedReviews[0].statusCd || ''
}

/** 初始化审核状态默认选中所有 */
const initDefaultStatus = () => {
  const allStatusOptions = getStrDictOptions(DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE)
  queryParams.statusCd = allStatusOptions.map(option => option.value)
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 使用新的接口支持多状态查询
    const { statusCd, ...otherParams } = queryParams
    const params = {
      ...otherParams,
      statusCdList: statusCd // 将statusCd数组传递给statusCdList参数，排除原statusCd字段
    }
    const data = await CertificateInfoApi.getCertificateInfoPageByStatus(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  initDefaultStatus() // 重置后恢复默认选中所有审核状态
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 打开证书弹窗 */
const openCertificate = (row: CertificateInfoVO) => {
  currentCertificateData.value = row
  certificateDialogVisible.value = true
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CertificateInfoApi.deleteCertificateInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    // 使用新的接口支持多状态查询
    const { statusCd, ...otherParams } = queryParams
    const params = {
      ...otherParams,
      statusCdList: statusCd // 将statusCd数组传递给statusCdList参数，排除原statusCd字段
    }
    const data = await CertificateInfoApi.exportCertificateInfo(params)
    download.excel(data, '存证信息表，用于记录各类数据存证相关信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  initDefaultStatus() // 初始化默认选中所有审核状态
  getList()
})
</script>
