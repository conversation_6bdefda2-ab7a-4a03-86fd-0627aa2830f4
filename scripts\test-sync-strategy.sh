#!/bin/bash

# 测试同步策略脚本
# 用于验证AI和IoT模块的同步策略是否正确配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "${YELLOW}🔍 $1${NC}"
}

echo
print_info "🧪 同步策略测试工具"
echo "=================================="
echo

# 检查上游模块存在性
print_step "检查上游模块存在性..."

modules_to_check=(
    "src/views/ai/"
    "src/api/ai/"
    "src/views/iot/"
    "src/api/iot/"
    "src/views/business/"
    "src/api/business/"
)

for module in "${modules_to_check[@]}"; do
    print_info "检查模块: $module"
    
    # 检查本地是否存在
    if [ -d "$module" ]; then
        echo "  📁 本地存在"
    else
        echo "  ❌ 本地不存在"
    fi
    
    # 检查上游是否存在
    if git cat-file -e upstream/master:"$module" 2>/dev/null; then
        echo "  🌐 上游存在"
        
        # 检查是否有差异
        if git diff --quiet HEAD upstream/master -- "$module" 2>/dev/null; then
            echo "  ✅ 无差异"
        else
            echo "  🔄 有差异，可同步"
        fi
    else
        echo "  ❌ 上游不存在"
    fi
    echo
done

# 检查配置文件
print_step "检查配置文件..."

config_files=(
    "sync.config.js"
    "scripts/sync-modules.js"
    "scripts/quick-sync.sh"
    "scripts/quick-sync.bat"
)

for config in "${config_files[@]}"; do
    print_info "检查配置: $config"
    
    if [ -f "$config" ]; then
        echo "  ✅ 文件存在"
        
        # 检查AI/IoT模块配置
        if grep -q "src/views/ai/" "$config" 2>/dev/null; then
            echo "  ✅ 包含AI模块配置"
        else
            echo "  ⚠️  未找到AI模块配置"
        fi
        
        if grep -q "src/views/iot/" "$config" 2>/dev/null; then
            echo "  ✅ 包含IoT模块配置"
        else
            echo "  ⚠️  未找到IoT模块配置"
        fi
        
        # 检查业务模块保护
        if grep -q "src/views/business/" "$config" 2>/dev/null; then
            echo "  ✅ 包含业务模块保护"
        else
            echo "  ⚠️  未找到业务模块保护"
        fi
    else
        echo "  ❌ 文件不存在"
    fi
    echo
done

# 模拟同步测试
print_step "模拟同步测试..."

print_info "创建测试分支..."
test_branch="test/sync-strategy-$(date +%Y%m%d-%H%M%S)"

if git checkout -b "$test_branch" 2>/dev/null; then
    print_success "测试分支创建成功: $test_branch"
    
    # 测试安全模块同步
    print_info "测试安全模块同步..."
    safe_modules=(
        "src/components/Form/"
        "src/utils/auth.ts"
        "types/"
    )
    
    for module in "${safe_modules[@]}"; do
        if git cat-file -e upstream/master:"$module" 2>/dev/null; then
            if git checkout upstream/master -- "$module" 2>/dev/null; then
                echo "  ✅ $module 同步成功"
            else
                echo "  ⚠️  $module 同步失败"
            fi
        else
            echo "  ⚠️  $module 上游不存在"
        fi
    done
    
    # 检查是否有变更
    if git diff --quiet --cached 2>/dev/null; then
        print_warning "没有检测到变更"
    else
        print_success "检测到变更，同步策略工作正常"
        echo "变更文件："
        git status --porcelain
    fi
    
    # 清理测试
    print_info "清理测试环境..."
    git checkout main 2>/dev/null
    git branch -D "$test_branch" 2>/dev/null
    print_success "测试环境清理完成"
    
else
    print_error "无法创建测试分支"
fi

echo
print_success "🎉 同步策略测试完成！"
echo
print_info "📋 测试总结："
echo "1. ✅ 上游模块存在性检查完成"
echo "2. ✅ 配置文件完整性检查完成"  
echo "3. ✅ 模拟同步测试完成"
echo
print_warning "💡 建议："
echo "- 定期运行此测试以验证同步策略"
echo "- 在实际同步前先运行测试"
echo "- 如发现问题请检查配置文件"
