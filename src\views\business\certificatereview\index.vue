<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="数据编号" prop="dataNumber">
        <el-input
          v-model="queryParams.dataNumber"
          placeholder="请输入数据编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据名称" prop="dataName">
        <el-input
          v-model="queryParams.dataName"
          placeholder="请输入数据名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="存证单位" prop="certificationUnit">
        <el-input
          v-model="queryParams.certificationUnit"
          placeholder="请输入存证单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据分类" prop="dataClassification">
        <el-select
          v-model="queryParams.dataClassification"
          placeholder="请选择数据分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_CLASSIFICATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据级别" prop="dataLevel">
        <el-select
          v-model="queryParams.dataLevel"
          placeholder="请选择数据级别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生成时间" prop="dataGenerationTime">
        <el-date-picker
          v-model="queryParams.dataGenerationTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <!-- <el-form-item label="数据规模" prop="dataScale">
        <el-input
          v-model="queryParams.dataScale"
          placeholder="请输入数据规模"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据规模单位" prop="dataScaleUnit">
        <el-select
          v-model="queryParams.dataScaleUnit"
          placeholder="请选择数据规模单位"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_SCALE_UNIT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="数据资源形态" prop="dataResourceForm">
        <el-select
          v-model="queryParams.dataResourceForm"
          placeholder="请选择数据资源形态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_RESOURCE_FORM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="数据块哈希值" prop="dataBlockHashValue">
        <el-input
          v-model="queryParams.dataBlockHashValue"
          placeholder="请输入数据块哈希值"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数据访问地址" prop="dataAccessAddress">
        <el-input
          v-model="queryParams.dataAccessAddress"
          placeholder="请输入数据访问地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="扩展信息哈希值" prop="extendedInfoHashValue">
        <el-input
          v-model="queryParams.extendedInfoHashValue"
          placeholder="请输入扩展信息哈希值"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="扩展信息访问地址" prop="extendedInfoAccessAddress">
        <el-input
          v-model="queryParams.extendedInfoAccessAddress"
          placeholder="请输入扩展信息访问地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="数据来源" prop="dataSourceInfo">
        <el-select
          v-model="queryParams.dataSourceInfo"
          placeholder="请选择数据来源"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DATA_SOURCE_INFO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="数据来源具体信息" prop="dataSourceSpecificInfo">
        <el-input
          v-model="queryParams.dataSourceSpecificInfo"
          placeholder="请输入数据来源具体信息"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="审核状态" prop="statusCd">
        <el-select
          v-model="queryParams.statusCd"
          placeholder="请选择审核状态"
          multiple
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in filteredReviewStatusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['business:certificate-info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['business:certificate-info:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @row-click="handleRowClick"
      :row-class-name="({row}) => selectedCertificateId === row.id ? 'selected-row' : ''"
    >
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="数据编号" align="center" prop="dataNumber" />
      <el-table-column label="数据名称" align="center" prop="dataName" />
      <el-table-column label="存证单位" align="center" prop="certificationUnit" />
      <el-table-column label="数据分类" align="center" prop="dataClassification">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_CLASSIFICATION" :value="scope.row.dataClassification" />
        </template>
      </el-table-column>
      <el-table-column label="数据级别" align="center" prop="dataLevel">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_LEVEL" :value="scope.row.dataLevel" />
        </template>
      </el-table-column>
      <el-table-column
        label="数据生成时间"
        align="center"
        prop="dataGenerationTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <!-- <el-table-column label="数据规模" align="center" prop="dataScale" />
      <el-table-column label="数据规模单位" align="center" prop="dataScaleUnit">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_SCALE_UNIT" :value="scope.row.dataScaleUnit" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="数据资源形态" align="center" prop="dataResourceForm">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_RESOURCE_FORM" :value="scope.row.dataResourceForm" />
        </template>
      </el-table-column>
      <el-table-column label="数据资源权属申明" align="center" prop="dataResourceOwnership">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_RESOURCE_OWNERSHIP" :value="scope.row.dataResourceOwnership" />
        </template>
      </el-table-column>
      <el-table-column label="数据样本文件路径" align="center" prop="dataSampleFile" />
      <el-table-column label="限制情况说明" align="center" prop="restrictionDescription" />
      <el-table-column label="其他描述" align="center" prop="otherDescription" />
      <el-table-column label="数据块哈希值" align="center" prop="dataBlockHashValue" />
      <el-table-column label="数据访问地址" align="center" prop="dataAccessAddress" />
      <el-table-column label="扩展信息哈希值" align="center" prop="extendedInfoHashValue" />
      <el-table-column label="扩展信息访问地址" align="center" prop="extendedInfoAccessAddress" /> -->
      <el-table-column label="数据来源信息" align="center" prop="dataSourceInfo">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_SOURCE_INFO" :value="scope.row.dataSourceInfo" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="数据来源具体信息" align="center" prop="dataSourceSpecificInfo" />
      <el-table-column label="数据来源佐证材料路径" align="center" prop="dataSourceEvidenceMaterial" />
      <el-table-column label="有效控制措施佐证材料路径" align="center" prop="effectiveControlEvidenceMaterial" />
      <el-table-column label="个人信息采集的合法佐证材料路径" align="center" prop="personalInfoCollectionEvidenceMaterial" /> -->
      <el-table-column label="审核状态" align="center" prop="statusCd">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE" :value="getLatestReviewStatus(scope.row.certificateReviews)" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['business:certificate-info:update']"
          >
            编辑
          </el-button> -->
          <el-button
            link
            type="success"
            @click="openReviewForm(scope.row, scope.row.certificateReviews?.[0]?.id)"
            v-hasPermi="['business:certificate-review:create']"
          >
            审核
          </el-button>
          <!-- <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['business:certificate-info:delete']"
          >
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 审核记录列表 -->
  <ContentWrap v-if="selectedCertificateId">
    <div class="mb-4">
      <h3 class="text-lg font-medium">审核记录</h3>
      <p class="text-sm text-gray-500">数据编号：{{ selectedCertificateData?.dataNumber }} - {{ selectedCertificateData?.dataName }}</p>
    </div>
    <el-table v-loading="reviewLoading" :data="reviewList" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="审核时间" align="center" prop="reviewDate" :formatter="timestampFormatter" width="180px" />
      <el-table-column label="审核结果" align="center" prop="reviewResults" />
      <el-table-column label="审核机构" align="center" prop="reviewOrgId" />
      <el-table-column label="权益主体合规性" align="center" prop="reviewDataSubjectComp">
        <template #default="scope">
          <el-tag
            v-if="scope.row.reviewDataSubjectComp !== null && scope.row.reviewDataSubjectComp !== undefined"
            :type="scope.row.reviewDataSubjectComp === 1 ? 'success' : 'danger'"
          >
            {{ scope.row.reviewDataSubjectComp === 1 ? '合规' : '不合规' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据来源合规性" align="center" prop="reviewDataSourceComp">
        <template #default="scope">
          <el-tag
            v-if="scope.row.reviewDataSourceComp !== null && scope.row.reviewDataSourceComp !== undefined"
            :type="scope.row.reviewDataSourceComp === 1 ? 'success' : 'danger'"
          >
            {{ scope.row.reviewDataSourceComp === 1 ? '合规' : '不合规' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据处理合规性" align="center" prop="reviewDataProcessComp">
        <template #default="scope">
          <el-tag
            v-if="scope.row.reviewDataProcessComp !== null && scope.row.reviewDataProcessComp !== undefined"
            :type="scope.row.reviewDataProcessComp === 1 ? 'success' : 'danger'"
          >
            {{ scope.row.reviewDataProcessComp === 1 ? '合规' : '不合规' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据内容合规性" align="center" prop="reviewDataContentComp">
        <template #default="scope">
          <el-tag
            v-if="scope.row.reviewDataContentComp !== null && scope.row.reviewDataContentComp !== undefined"
            :type="scope.row.reviewDataContentComp === 1 ? 'success' : 'danger'"
          >
            {{ scope.row.reviewDataContentComp === 1 ? '合规' : '不合规' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="statusCd">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE" :value="scope.row.statusCd" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CertificateInfoForm ref="formRef" @success="getList" />
  <!-- 审核表单弹窗 -->
  <CertificateReviewForm ref="reviewFormRef" @success="handleReviewSuccess" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter, timestampFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CertificateInfoApi, CertificateInfoVO, CertificateReviewVO } from '@/api/business/certificateinfo'
import { CertificateReviewApi, CertificateReviewRespVO } from '@/api/business/certificatereview'
import CertificateReviewForm from './CertificateReviewForm.vue'

/** 存证信息表，用于记录各类数据存证相关信息 列表 */
defineOptions({ name: 'CertificateInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CertificateInfoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

// 审核记录相关
const reviewLoading = ref(false) // 审核记录列表的加载中
const reviewList = ref<CertificateReviewRespVO[]>([]) // 审核记录列表的数据
const selectedCertificateId = ref<number | null>(null) // 当前选中的数据存证ID
const selectedCertificateData = ref<CertificateInfoVO | null>(null) // 当前选中的数据存证数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dataNumber: undefined,
  dataName: undefined,
  certificationUnit: undefined,
  dataClassification: undefined,
  dataLevel: undefined,
  dataGenerationTime: [],
  dataScale: undefined,
  dataScaleUnit: undefined,
  dataResourceForm: undefined,
  dataResourceOwnership: undefined,
  dataSampleFile: undefined,
  restrictionDescription: undefined,
  otherDescription: undefined,
  dataBlockHashValue: undefined,
  dataAccessAddress: undefined,
  extendedInfoHashValue: undefined,
  extendedInfoAccessAddress: undefined,
  dataSourceInfo: undefined,
  dataSourceSpecificInfo: undefined,
  dataSourceEvidenceMaterial: undefined,
  effectiveControlEvidenceMaterial: undefined,
  personalInfoCollectionEvidenceMaterial: undefined,
  statusCd: ['A', 'B', 'D'], // 默认只查询已提交、审核中和审核拒绝的数据
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 过滤审核状态选项，只显示已提交、审核中和审核拒绝的选项
const filteredReviewStatusOptions = computed(() => {
  const allOptions = getStrDictOptions(DICT_TYPE.DATA_EVIDENCE_REVIEW_NODE)
  // 只保留已提交(A)、审核中(B)和审核拒绝(D)的选项
  const allowedValues = ['A', 'B', 'D']
  return allOptions.filter(option => allowedValues.includes(option.value))
})

/** 获取最新的审核状态 */
const getLatestReviewStatus = (certificateReviews: CertificateReviewVO[]): string => {
  if (!certificateReviews || certificateReviews.length === 0) {
    return '' // 如果没有审核记录，返回空字符串
  }

  // 按创建时间降序排序，获取最新的审核记录
  const sortedReviews = certificateReviews.sort((a, b) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  })

  return sortedReviews[0].statusCd || ''
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 使用新的接口支持多状态查询
    const { statusCd, createTime, ...otherParams } = queryParams
    const params = {
      ...otherParams,
      statusCdList: statusCd, // 将statusCd数组传递给statusCdList参数，排除原statusCd字段
      // 将时间字符串转换为时间戳
      createTime: createTime && createTime.length === 2
        ? [new Date(createTime[0]).getTime(), new Date(createTime[1]).getTime()]
        : undefined
    }
    const data = await CertificateInfoApi.getCertificateInfoPageByStatus(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 重置后恢复默认的审核状态过滤
  queryParams.statusCd = ['A', 'B', 'D']
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 审核表单操作 */
const reviewFormRef = ref()
const openReviewForm = (data: CertificateInfoVO, reviewId?: number) => {
  reviewFormRef.value.open('review', data.id, data, reviewId)
}

/** 获取审核记录列表 */
const getReviewList = async (certificateId: number) => {
  reviewLoading.value = true
  try {
    const data = await CertificateReviewApi.getCertificateReviewPage({
      reviewDataId: certificateId,
      pageNo: 1,
      pageSize: 100 // 获取所有审核记录
    })
    reviewList.value = data.list || []
  } finally {
    reviewLoading.value = false
  }
}

/** 处理行点击事件 */
const handleRowClick = (row: CertificateInfoVO) => {
  selectedCertificateId.value = row.id
  selectedCertificateData.value = row

  // 优先使用存证信息中内嵌的审核记录数据
  if (row.certificateReviews && row.certificateReviews.length > 0) {
    // 将内嵌的审核记录转换为响应格式
    reviewList.value = row.certificateReviews.map(review => ({
      ...review,
      // 确保数据格式一致
      reviewDate: review.reviewDate || review.createTime,
    }))
    reviewLoading.value = false
  } else {
    // 如果没有内嵌数据，则调用API获取
    getReviewList(row.id)
  }
}

/** 审核成功后的回调 */
const handleReviewSuccess = () => {
  // 刷新主列表，获取最新的存证信息（包含最新的审核记录）
  getList()
  // 如果有选中的数据存证，重新获取其审核记录以确保数据最新
  if (selectedCertificateId.value) {
    // 强制调用API获取最新的审核记录，确保数据同步
    getReviewList(selectedCertificateId.value)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CertificateInfoApi.deleteCertificateInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CertificateInfoApi.exportCertificateInfo(queryParams)
    download.excel(data, '存证信息表，用于记录各类数据存证相关信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.selected-row {
  background-color: #f0f9ff !important;
}

.selected-row:hover {
  background-color: #e0f2fe !important;
}

.el-table__row {
  cursor: pointer;
}
</style>
