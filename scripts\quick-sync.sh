#!/bin/bash

# 文创链服务平台 2.0 - 上游快速同步脚本
# 用于安全地同步芋道源码的基础组件和工具函数更新
# 
# 使用方法:
#   chmod +x scripts/quick-sync.sh
#   ./scripts/quick-sync.sh
#
# 或者:
#   bash scripts/quick-sync.sh

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 检查是否在 Git 仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "当前目录不是 Git 仓库"
        exit 1
    fi
}

# 检查是否有未提交的更改
check_clean_working_tree() {
    if ! git diff-index --quiet HEAD --; then
        print_warning "工作目录有未提交的更改"
        echo "请先提交或暂存您的更改："
        git status --porcelain
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "同步已取消"
            exit 0
        fi
    fi
}

# 检查上游仓库配置
check_upstream_remote() {
    if ! git remote get-url upstream > /dev/null 2>&1; then
        print_step "配置上游仓库..."
        git remote add upstream https://gitee.com/yudaocode/yudao-ui-admin-vue3.git
        print_success "上游仓库配置完成"
    else
        print_info "上游仓库已配置"
    fi
}

# 获取上游更新
fetch_upstream() {
    print_step "获取上游更新..."
    if git fetch upstream; then
        print_success "上游更新获取完成"
    else
        print_error "获取上游更新失败"
        exit 1
    fi
}

# 检查上游更新数量
check_upstream_updates() {
    local updates
    updates=$(git rev-list HEAD..upstream/master --count 2>/dev/null || echo "0")
    
    if [ "$updates" -eq 0 ]; then
        print_success "无新更新，当前已是最新版本"
        exit 0
    else
        print_info "发现 $updates 个新提交"
        return $updates
    fi
}

# 显示上游更新日志
show_upstream_log() {
    print_info "📋 最近的上游更新日志："
    echo "----------------------------------------"
    git log --oneline HEAD..upstream/master | head -10
    echo "----------------------------------------"
    echo
}

# 创建同步分支
create_sync_branch() {
    local branch_name="sync/quick-$(date +%Y%m%d-%H%M%S)"
    
    print_step "创建同步分支: $branch_name"
    if git checkout -b "$branch_name"; then
        print_success "同步分支创建成功"
        echo "$branch_name"
    else
        print_error "创建同步分支失败"
        exit 1
    fi
}

# 同步安全模块
sync_safe_modules() {
    print_step "开始同步安全模块..."
    
    local synced_count=0
    local skipped_count=0
    
    # 定义安全同步的模块
    local safe_modules=(
        "src/components/Form/"
        "src/components/Table/"
        "src/components/Dialog/"
        "src/components/Upload/"
        "src/components/Icon/"
        "src/components/Editor/"
        "src/utils/auth.ts"
        "src/utils/dict.ts"
        "src/utils/download.ts"
        "src/utils/validate.ts"
        "src/utils/tree.ts"
        "types/"
    )

    # 定义需要谨慎同步的模块（需要用户确认）
    local careful_modules=(
        "src/views/ai/"
        "src/api/ai/"
        "src/views/iot/"
        "src/api/iot/"
    )
    
    for module in "${safe_modules[@]}"; do
        print_info "检查模块: $module"
        
        # 检查模块是否存在于上游
        if git cat-file -e upstream/master:"$module" 2>/dev/null; then
            # 检查是否有变更
            if git diff --quiet HEAD upstream/master -- "$module" 2>/dev/null; then
                echo "  ⏭️  无变更，跳过"
                ((skipped_count++))
            else
                # 有变更，进行同步
                if git checkout upstream/master -- "$module" 2>/dev/null; then
                    echo "  ✅ 同步成功"
                    ((synced_count++))
                else
                    echo "  ⚠️  同步失败"
                fi
            fi
        else
            echo "  ⚠️  上游不存在"
            ((skipped_count++))
        fi
    done
    
    print_success "安全模块同步完成: $synced_count 个已同步, $skipped_count 个跳过"
    return $synced_count
}

# 同步需要谨慎处理的模块
sync_careful_modules() {
    print_step "检查需要谨慎同步的模块..."

    local synced_count=0
    local skipped_count=0

    for module in "${careful_modules[@]}"; do
        print_info "检查模块: $module"

        # 检查模块是否存在于上游
        if git cat-file -e upstream/master:"$module" 2>/dev/null; then
            # 检查是否有变更
            if git diff --quiet HEAD upstream/master -- "$module" 2>/dev/null; then
                echo "  ⏭️  无变更，跳过"
                ((skipped_count++))
            else
                # 有变更，询问用户是否同步
                echo "  ⚠️  发现变更"
                read -p "    是否同步 $module？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    if git checkout upstream/master -- "$module" 2>/dev/null; then
                        echo "    ✅ 同步成功"
                        ((synced_count++))
                    else
                        echo "    ❌ 同步失败"
                    fi
                else
                    echo "    ⏭️  用户跳过"
                    ((skipped_count++))
                fi
            fi
        else
            echo "  ⚠️  上游不存在"
            ((skipped_count++))
        fi
    done

    print_success "谨慎模块处理完成: $synced_count 个已同步, $skipped_count 个跳过"
    return $synced_count
}

# 检查同步结果
check_sync_result() {
    print_step "检查同步结果..."
    
    # 检查是否有变更
    if git diff --quiet --cached; then
        print_warning "没有检测到任何变更"
        return 1
    else
        print_info "检测到以下变更："
        git status --porcelain
        return 0
    fi
}

# 运行基本验证
run_basic_validation() {
    print_step "运行基本验证..."
    
    # 检查 package.json 语法
    if command -v node > /dev/null 2>&1; then
        if node -e "JSON.parse(require('fs').readFileSync('package.json', 'utf8'))" 2>/dev/null; then
            print_success "package.json 语法正确"
        else
            print_error "package.json 语法错误"
            return 1
        fi
    fi
    
    # 检查 TypeScript 配置
    if [ -f "tsconfig.json" ]; then
        if command -v node > /dev/null 2>&1; then
            if node -e "JSON.parse(require('fs').readFileSync('tsconfig.json', 'utf8'))" 2>/dev/null; then
                print_success "tsconfig.json 语法正确"
            else
                print_error "tsconfig.json 语法错误"
                return 1
            fi
        fi
    fi
    
    print_success "基本验证通过"
    return 0
}

# 提交同步结果
commit_sync_result() {
    print_step "提交同步结果..."
    
    git add .
    
    local commit_message="🔄 上游同步: 安全同步基础组件和工具函数

✅ 已同步的模块:
- 基础组件 (Form、Table、Dialog、Upload、Icon、Editor)
- 工具函数 (auth.ts、dict.ts、download.ts、validate.ts、tree.ts)
- 类型定义 (types/)

🛡️ 保护的业务模块:
- 文创链业务模块 (src/views/business/)
- AI 模块 (src/views/ai/)
- 物联网模块 (src/views/iot/)

🎯 来源: 芋道源码上游更新
📅 同步时间: $(date '+%Y-%m-%d %H:%M:%S')"

    if git commit -m "$commit_message" --no-verify; then
        print_success "同步结果提交成功"
    else
        print_error "提交失败"
        return 1
    fi
}

# 显示后续操作建议
show_next_steps() {
    local sync_branch="$1"
    
    echo
    print_success "🎉 上游同步完成！"
    echo
    print_info "📋 后续操作建议："
    echo "1. 测试功能是否正常："
    echo "   pnpm run dev"
    echo "   pnpm run lint:eslint"
    echo "   pnpm run ts:check"
    echo
    echo "2. 如果测试通过，合并到主分支："
    echo "   git checkout main"
    echo "   git merge $sync_branch"
    echo "   git branch -d $sync_branch"
    echo
    echo "3. 如果有问题，可以回滚："
    echo "   git checkout main"
    echo "   git branch -D $sync_branch"
    echo
    print_warning "⚠️  请务必测试后再合并到主分支！"
}

# 主函数
main() {
    echo
    print_info "🚀 文创链服务平台 2.0 - 上游快速同步工具"
    echo "=================================================="
    echo
    
    # 检查环境
    check_git_repo
    check_clean_working_tree
    check_upstream_remote
    
    # 获取更新
    fetch_upstream
    check_upstream_updates
    show_upstream_log
    
    # 确认同步
    read -p "是否继续执行同步？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "同步已取消"
        exit 0
    fi
    
    # 执行同步
    sync_branch=$(create_sync_branch)

    # 同步安全模块
    safe_synced=0
    careful_synced=0

    if sync_safe_modules; then
        safe_synced=$?
    fi

    # 询问是否同步谨慎模块
    echo
    print_info "发现 AI 和 IoT 模块，这些模块来自原框架但可能有定制"
    read -p "是否检查并选择性同步这些模块？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if sync_careful_modules; then
            careful_synced=$?
        fi
    fi

    total_synced=$((safe_synced + careful_synced))

    if [ $total_synced -gt 0 ] && check_sync_result; then
        if run_basic_validation; then
            commit_sync_result
            show_next_steps "$sync_branch"
        else
            print_error "验证失败，请检查同步结果"
            exit 1
        fi
    else
        print_warning "没有需要同步的内容"
        git checkout main
        git branch -D "$sync_branch" 2>/dev/null || true
    fi
}

# 运行主函数
main "$@"
