#!/usr/bin/env node

/**
 * 模块化同步脚本
 * 用于选择性同步芋道源码的更新
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 配置可同步的模块
const SYNC_MODULES = {
  // 基础组件 - 安全同步
  components: {
    paths: [
      'src/components/Form/',
      'src/components/Table/',
      'src/components/Dialog/',
      'src/components/Upload/',
      'src/components/Icon/',
      'src/components/Editor/',
      'src/components/Descriptions/'
    ],
    safe: true,
    description: '基础组件更新'
  },
  
  // 工具函数 - 安全同步
  utils: {
    paths: [
      'src/utils/auth.ts',
      'src/utils/request.ts',
      'src/utils/validate.ts',
      'src/utils/tree.ts',
      'src/utils/dict.ts',
      'src/utils/download.ts'
    ],
    safe: true,
    description: '工具函数更新'
  },
  
  // 类型定义 - 安全同步
  types: {
    paths: [
      'types/components.d.ts',
      'types/env.d.ts',
      'types/global.d.ts',
      'types/router.d.ts'
    ],
    safe: true,
    description: '类型定义更新'
  },
  
  // 系统管理模块 - 需要谨慎
  system: {
    paths: [
      'src/views/system/user/',
      'src/views/system/role/',
      'src/views/system/menu/',
      'src/views/system/dept/',
      'src/api/system/'
    ],
    safe: false,
    description: '系统管理模块更新'
  },

  // 基础设施模块 - 需要谨慎
  infra: {
    paths: [
      'src/views/infra/codegen/',
      'src/views/infra/config/',
      'src/views/infra/file/',
      'src/api/infra/'
    ],
    safe: false,
    description: '基础设施模块更新'
  },

  // AI 模块 - 需要谨慎（原框架提供，但可能有定制）
  ai: {
    paths: [
      'src/views/ai/chat/',
      'src/views/ai/image/',
      'src/views/ai/knowledge/',
      'src/views/ai/mindmap/',
      'src/views/ai/model/',
      'src/views/ai/music/',
      'src/views/ai/write/',
      'src/api/ai/'
    ],
    safe: false,
    description: 'AI 智能模块更新'
  },

  // IoT 模块 - 需要谨慎（原框架提供，但可能有定制）
  iot: {
    paths: [
      'src/views/iot/device/',
      'src/views/iot/product/',
      'src/api/iot/'
    ],
    safe: false,
    description: '物联网模块更新'
  },
  
  // 构建配置 - 需要谨慎
  build: {
    paths: [
      'build/',
      'vite.config.ts',
      'tsconfig.json',
      'uno.config.ts'
    ],
    safe: false,
    description: '构建配置更新'
  }
}

// 业务模块黑名单 - 绝不同步
const BUSINESS_BLACKLIST = [
  'src/views/business/',      // 文创链特有业务模块
  'src/api/business/',        // 文创链特有业务接口
  'src/config/business.ts',  // 业务配置文件
  'src/assets/business/',    // 业务相关资源
  // 添加其他项目特有的业务相关路径
]

class UpstreamSyncer {
  constructor() {
    this.upstreamRemote = 'upstream'
    this.upstreamBranch = 'master'
    this.currentBranch = this.getCurrentBranch()
  }

  getCurrentBranch() {
    try {
      return execSync('git branch --show-current', { encoding: 'utf8' }).trim()
    } catch (error) {
      console.error('❌ 获取当前分支失败:', error.message)
      process.exit(1)
    }
  }

  checkUpstreamRemote() {
    try {
      execSync(`git remote get-url ${this.upstreamRemote}`, { stdio: 'ignore' })
      return true
    } catch {
      return false
    }
  }

  setupUpstreamRemote() {
    if (!this.checkUpstreamRemote()) {
      console.log('🔧 配置上游仓库...')
      execSync(`git remote add ${this.upstreamRemote} https://gitee.com/yudaocode/yudao-ui-admin-vue3.git`)
      console.log('✅ 上游仓库配置完成')
    }
  }

  fetchUpstream() {
    console.log('📡 获取上游更新...')
    try {
      execSync(`git fetch ${this.upstreamRemote}`, { stdio: 'inherit' })
      console.log('✅ 上游更新获取完成')
    } catch (error) {
      console.error('❌ 获取上游更新失败:', error.message)
      process.exit(1)
    }
  }

  checkUpstreamChanges() {
    try {
      const result = execSync(
        `git rev-list HEAD..${this.upstreamRemote}/${this.upstreamBranch} --count`,
        { encoding: 'utf8' }
      ).trim()
      return parseInt(result)
    } catch (error) {
      console.error('❌ 检查上游变更失败:', error.message)
      return 0
    }
  }

  getUpstreamLog() {
    try {
      return execSync(
        `git log --oneline HEAD..${this.upstreamRemote}/${this.upstreamBranch}`,
        { encoding: 'utf8' }
      ).trim()
    } catch (error) {
      return ''
    }
  }

  createSyncBranch() {
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    const syncBranch = `sync/upstream-${timestamp}`
    
    try {
      execSync(`git checkout -b ${syncBranch}`)
      console.log(`✅ 创建同步分支: ${syncBranch}`)
      return syncBranch
    } catch (error) {
      console.error('❌ 创建同步分支失败:', error.message)
      process.exit(1)
    }
  }

  syncModule(moduleName, moduleConfig) {
    console.log(`\n🔄 同步模块: ${moduleName} - ${moduleConfig.description}`)
    
    let hasChanges = false
    
    for (const filePath of moduleConfig.paths) {
      try {
        // 检查文件是否存在于上游
        execSync(`git cat-file -e ${this.upstreamRemote}/${this.upstreamBranch}:${filePath}`, { stdio: 'ignore' })
        
        // 检查是否有变更
        try {
          execSync(`git diff --quiet HEAD ${this.upstreamRemote}/${this.upstreamBranch} -- ${filePath}`, { stdio: 'ignore' })
          console.log(`  ⏭️  ${filePath} (无变更)`)
        } catch {
          // 有变更，进行同步
          execSync(`git checkout ${this.upstreamRemote}/${this.upstreamBranch} -- ${filePath}`)
          console.log(`  ✅ ${filePath} (已同步)`)
          hasChanges = true
        }
      } catch {
        console.log(`  ⚠️  ${filePath} (上游不存在或已删除)`)
      }
    }
    
    return hasChanges
  }

  syncDependencies() {
    console.log('\n📦 检查依赖更新...')
    
    try {
      // 获取上游 package.json
      const upstreamPackage = execSync(
        `git show ${this.upstreamRemote}/${this.upstreamBranch}:package.json`,
        { encoding: 'utf8' }
      )
      
      const currentPackage = fs.readFileSync('package.json', 'utf8')
      
      const upstreamPkg = JSON.parse(upstreamPackage)
      const currentPkg = JSON.parse(currentPackage)
      
      // 比较依赖版本
      const updates = this.compareDependencies(currentPkg, upstreamPkg)
      
      if (updates.length > 0) {
        console.log('📋 发现依赖更新:')
        updates.forEach(update => {
          console.log(`  ${update.name}: ${update.current} → ${update.upstream}`)
        })
        
        // 这里可以选择性更新依赖
        console.log('💡 请手动检查并更新需要的依赖版本')
      } else {
        console.log('✅ 依赖版本已是最新')
      }
    } catch (error) {
      console.log('⚠️  无法比较依赖版本:', error.message)
    }
  }

  compareDependencies(current, upstream) {
    const updates = []
    const allDeps = { ...current.dependencies, ...current.devDependencies }
    const upstreamDeps = { ...upstream.dependencies, ...upstream.devDependencies }
    
    for (const [name, version] of Object.entries(upstreamDeps)) {
      if (allDeps[name] && allDeps[name] !== version) {
        updates.push({
          name,
          current: allDeps[name],
          upstream: version
        })
      }
    }
    
    return updates
  }

  async run() {
    console.log('🚀 开始上游同步流程...\n')
    
    // 1. 设置上游仓库
    this.setupUpstreamRemote()
    
    // 2. 获取上游更新
    this.fetchUpstream()
    
    // 3. 检查是否有更新
    const changeCount = this.checkUpstreamChanges()
    if (changeCount === 0) {
      console.log('✅ 上游无新更新')
      return
    }
    
    console.log(`📦 发现 ${changeCount} 个新提交`)
    console.log('\n📋 上游更新日志:')
    console.log(this.getUpstreamLog())
    
    // 4. 创建同步分支
    const syncBranch = this.createSyncBranch()
    
    // 5. 选择性同步模块
    let totalChanges = false
    
    for (const [moduleName, moduleConfig] of Object.entries(SYNC_MODULES)) {
      if (moduleConfig.safe) {
        const hasChanges = this.syncModule(moduleName, moduleConfig)
        totalChanges = totalChanges || hasChanges
      } else {
        console.log(`\n⚠️  跳过模块: ${moduleName} - ${moduleConfig.description} (需要手动检查)`)
      }
    }
    
    // 6. 检查依赖更新
    this.syncDependencies()
    
    // 7. 总结
    console.log('\n📊 同步完成总结:')
    if (totalChanges) {
      console.log('✅ 已同步安全模块的更新')
      console.log('💡 请检查变更并测试功能')
      console.log(`💡 如需同步其他模块，请手动执行: git checkout ${this.upstreamRemote}/${this.upstreamBranch} -- <路径>`)
    } else {
      console.log('ℹ️  安全模块无需更新')
    }
    
    console.log(`\n🔧 当前在同步分支: ${syncBranch}`)
    console.log('🔧 测试完成后可合并到主分支')
  }
}

// 运行同步器
const syncer = new UpstreamSyncer()
syncer.run().catch(error => {
  console.error('❌ 同步过程出错:', error)
  process.exit(1)
})
