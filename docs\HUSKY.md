# Husky Git Hooks 配置说明

## 📖 简介

本项目使用 [<PERSON><PERSON>](https://typicode.github.io/husky/) 来管理 Git hooks，确保代码质量和提交规范。

## 🛠️ 已配置的 Git Hooks

### 1. pre-commit

在每次 `git commit` 前自动执行：

- **lint-staged**: 对暂存区文件进行代码检查和格式化
- **TypeScript 类型检查**: 确保类型安全
- **ESLint**: JavaScript/TypeScript 代码检查
- **Stylelint**: CSS/SCSS 样式检查
- **Prettier**: 代码格式化

### 2. commit-msg

在提交信息输入后执行：

- **commitlint**: 检查提交信息格式是否符合规范

### 3. pre-push

在 `git push` 前执行：

- **完整的代码检查**: ESLint、Stylelint、Prettier
- **TypeScript 类型检查**: 确保类型安全
- **构建测试**: 确保代码可以正常构建

## 📝 提交信息规范

### 格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type 类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `feat` | 新功能 | `feat(user): 添加用户管理功能` |
| `fix` | 修复 bug | `fix(login): 修复登录页面样式问题` |
| `docs` | 文档变更 | `docs(readme): 更新项目文档` |
| `style` | 代码格式 | `style(button): 调整按钮样式` |
| `refactor` | 代码重构 | `refactor(api): 重构用户API接口` |
| `perf` | 性能优化 | `perf(table): 优化表格渲染性能` |
| `test` | 添加测试 | `test(utils): 添加工具函数单元测试` |
| `chore` | 构建过程或辅助工具的变动 | `chore(deps): 更新依赖包版本` |
| `ci` | CI/CD 相关 | `ci(github): 更新 GitHub Actions 配置` |
| `build` | 构建相关 | `build(webpack): 优化打包配置` |
| `revert` | 回退 | `revert: 回退用户管理功能` |

### Scope 范围（可选）

常用的 scope 包括：

- `user`: 用户相关
- `auth`: 认证相关
- `api`: API 接口
- `ui`: 用户界面
- `config`: 配置相关
- `deps`: 依赖相关

## 🚀 使用方法

### 1. 安装配置

```bash
# 运行安装脚本（Linux/macOS）
chmod +x scripts/setup-husky.sh
./scripts/setup-husky.sh

# 或者运行 Windows 脚本
scripts/setup-husky.bat
```

### 2. 交互式提交

使用 commitizen 进行交互式提交：

```bash
# 使用交互式提交
npm run commit

# 或者
pnpm commit
```

### 3. 手动提交

如果你熟悉提交规范，也可以手动提交：

```bash
git add .
git commit -m "feat(user): 添加用户管理功能"
```

## 🔧 配置文件说明

### commitlint.config.js

- 定义提交信息格式规范
- 配置允许的 type 类型
- 设置提交信息长度限制

### .lintstagedrc.js

- 配置 lint-staged 规则
- 定义不同文件类型的处理方式
- 设置代码检查和格式化流程

### .husky/ 目录

- `pre-commit`: 提交前检查脚本
- `commit-msg`: 提交信息检查脚本
- `pre-push`: 推送前检查脚本
- `_/husky.sh`: Husky 核心脚本

## 🚫 跳过 Hooks

在某些特殊情况下，你可能需要跳过 hooks：

```bash
# 跳过 pre-commit hook
git commit --no-verify -m "fix: 紧急修复"

# 跳过 pre-push hook
git push --no-verify
```

**注意**: 请谨慎使用 `--no-verify`，只在紧急情况下使用。

## 🛠️ 故障排除

### 1. Hooks 不执行

```bash
# 重新安装 husky
npx husky install

# 检查 hooks 文件权限（Linux/macOS）
chmod +x .husky/pre-commit
chmod +x .husky/commit-msg
chmod +x .husky/pre-push
```

### 2. 提交信息格式错误

确保提交信息符合规范：

```bash
# ❌ 错误格式
git commit -m "fix bug"

# ✅ 正确格式
git commit -m "fix(login): 修复登录页面样式问题"
```

### 3. 代码检查失败

```bash
# 手动运行检查
npm run lint:eslint
npm run lint:style
npm run ts:check

# 修复后重新提交
git add .
git commit -m "fix(style): 修复代码格式问题"
```

## 📚 相关文档

- [Husky 官方文档](https://typicode.github.io/husky/)
- [Commitlint 官方文档](https://commitlint.js.org/)
- [Conventional Commits 规范](https://www.conventionalcommits.org/)
- [lint-staged 官方文档](https://github.com/okonet/lint-staged)
