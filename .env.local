# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 本地开发端口号
VITE_PORT=3000

# 是否自动打开浏览器（调试时设为false，避免开启两个浏览器）
VITE_OPEN=false

# 请求路径
VITE_BASE_URL='http://localhost:48080'
# VITE_BASE_URL='https://software-reg.bmark.cn/api'
# VITE_BASE_URL='http://**************:49018'
# VITE_BASE_URL='https://manage.bs-cde.cn/api'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap（开发环境启用以支持调试）
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'