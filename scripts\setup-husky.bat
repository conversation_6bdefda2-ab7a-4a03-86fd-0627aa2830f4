@echo off
chcp 65001 >nul

echo 🚀 开始配置 Husky Git hooks...

REM 检查是否在 Git 仓库中
if not exist ".git" (
  echo ❌ 错误：当前目录不是 Git 仓库，请先初始化 Git 仓库
  echo    运行: git init
  exit /b 1
)

REM 检查是否安装了 Node.js
node --version >nul 2>&1
if errorlevel 1 (
  echo ❌ 错误：未找到 Node.js，请先安装 Node.js
  exit /b 1
)

REM 检查是否安装了 pnpm
pnpm --version >nul 2>&1
if errorlevel 1 (
  echo ❌ 错误：未找到 pnpm，请先安装 pnpm
  echo    运行: npm install -g pnpm
  exit /b 1
)

REM 安装依赖
echo 📦 安装项目依赖...
pnpm install

REM 初始化 Husky
echo 🔧 初始化 Husky...
npx husky install

echo ✅ Husky 配置完成！
echo.
echo 📋 已配置的 Git hooks：
echo    • pre-commit:  代码检查和格式化
echo    • commit-msg:  提交信息格式检查
echo    • pre-push:    推送前完整检查
echo.
echo 💡 使用提示：
echo    • 使用 'npm run commit' 进行交互式提交
echo    • 提交信息格式: ^<type^>^(^<scope^>^): ^<subject^>
echo    • 示例: feat^(user^): 添加用户管理功能
echo.
echo 🎉 现在你可以开始开发了！

pause
