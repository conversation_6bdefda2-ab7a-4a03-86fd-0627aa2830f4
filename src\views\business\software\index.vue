<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="登记类型" prop="softwareType">
        <el-select
          v-model="queryParams.softwareType"
          placeholder="请选著登记类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="取得方式" prop="acquisitionMethod">
        <el-select
          v-model="queryParams.acquisitionMethod"
          placeholder="请选择取得方式"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_ACQUISITION_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="完整名称" prop="softwareFullName">
        <el-input
          v-model="queryParams.softwareFullName"
          placeholder="请输入完整名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="版本编号" prop="versionNumber">
        <el-input
          v-model="queryParams.versionNumber"
          placeholder="请输入版本编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="权利范围" prop="rightsScope">
        <el-select
          v-model="queryParams.rightsScope"
          placeholder="请选择权利范围"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_RIGHTS_SCOPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="平台分类" prop="platformType">
        <el-select
          v-model="queryParams.platformType"
          placeholder="请选择平台分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_PLATFORM_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软件分类" prop="softwareClassification">
        <el-select
          v-model="queryParams.softwareClassification"
          placeholder="请选择软件分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_CLASSIFICATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="软件说明" prop="softwareDescription">
        <el-select
          v-model="queryParams.softwareDescription"
          placeholder="请选择软件说明"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DESCRIPTION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="开发方式" prop="developmentMethod">
        <el-select
          v-model="queryParams.developmentMethod"
          placeholder="请选择开发方式"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DEVELOPMENT_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="编程语言" prop="programmingLanguages">
        <el-select
          v-model="queryParams.programmingLanguages"
          placeholder="请选择编程语言"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_PROGRAMMING_LANGUAGES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="编程语言描述" prop="programmingLanguagesDescribe">
        <el-input
          v-model="queryParams.programmingLanguagesDescribe"
          placeholder="请输入编程语言描述"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="开发完成日期" prop="completionDate">
        <el-date-picker
          v-model="queryParams.completionDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="登记邮箱" prop="registrantEmail">
        <el-input
          v-model="queryParams.registrantEmail"
          placeholder="请输入登记邮箱"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="登记手机号" prop="registrantPhone">
        <el-input
          v-model="queryParams.registrantPhone"
          placeholder="请输入登记手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="详细介绍内容" prop="softwareIntroduction">
        <el-input
          v-model="queryParams.softwareIntroduction"
          placeholder="请输入详细介绍内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="软著发表状态" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选择软著发表状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="开发的硬件环境" prop="developmentHardwareEnvironment">
        <el-input
          v-model="queryParams.developmentHardwareEnvironment"
          placeholder="请输入开发的硬件环境"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="运行的硬件环境" prop="runningHardwareEnvironment">
        <el-input
          v-model="queryParams.runningHardwareEnvironment"
          placeholder="请输入运行的硬件环境"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="开发该软件的操作系统" prop="developmentOs">
        <el-input
          v-model="queryParams.developmentOs"
          placeholder="请输入开发该软件的操作系统"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="软件开发环境/开发工具" prop="developmentTools">
        <el-input
          v-model="queryParams.developmentTools"
          placeholder="请输入软件开发环境/开发工具"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="该软件的运行平台/操作系统" prop="runningPlatformOs">
        <el-input
          v-model="queryParams.runningPlatformOs"
          placeholder="请输入该软件的运行平台/操作系统"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="软件运行支撑环境/支持软件" prop="runningSupportEnvironment">
        <el-input
          v-model="queryParams.runningSupportEnvironment"
          placeholder="请输入软件运行支撑环境/支持软件"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="源程序的行数" prop="sourceCodeLines">
        <el-input
          v-model="queryParams.sourceCodeLines"
          placeholder="请输入源程序的行数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="开发目的" prop="developmentPurpose">
        <el-input
          v-model="queryParams.developmentPurpose"
          placeholder="请输入开发目的"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="面向领域/行业" prop="targetDomain">
        <el-input
          v-model="queryParams.targetDomain"
          placeholder="请输入面向领域/行业"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="软件的主要功能" prop="mainFunction">
        <el-input
          v-model="queryParams.mainFunction"
          placeholder="请输入软件的主要功能"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="软件的技术特点，多种特点以逗号分隔" prop="technicalFeatures">
        <el-select
          v-model="queryParams.technicalFeatures"
          placeholder="请选择软件的技术特点，多种特点以逗号分隔"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_TECHNICAL_FEATURES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软件技术特点描述" prop="technicalFeaturesDescribe">
        <el-input
          v-model="queryParams.technicalFeaturesDescribe"
          placeholder="请输入软件技术特点描述"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="程序鉴别材料的提交方式" prop="programIdentificationType">
        <el-select
          v-model="queryParams.programIdentificationType"
          placeholder="请选择程序鉴别材料的提交方式"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DEPOSIT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="程序鉴别材料ID，多个以逗号隔开" prop="programIdentificationIds">
        <el-input
          v-model="queryParams.programIdentificationIds"
          placeholder="请输入程序鉴别材料ID，多个以逗号隔开"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="文档鉴别材料的提交方式" prop="documentIdentificationType">
        <el-select
          v-model="queryParams.documentIdentificationType"
          placeholder="请选择文档鉴别材料的提交方式"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SOFTWARE_DEPOSIT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文档鉴别材料ID，多个以逗号隔开" prop="documentIdentificationIds">
        <el-input
          v-model="queryParams.documentIdentificationIds"
          placeholder="请输入文档鉴别材料ID，多个以逗号隔开"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="其他相关证明文件ID，多个以逗号隔开" prop="otherCertificationIds">
        <el-input
          v-model="queryParams.otherCertificationIds"
          placeholder="请输入其他相关证明文件ID，多个以逗号隔开"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="statusCd">
        <el-input
          v-model="queryParams.statusCd"
          placeholder="请输入状态"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['business:software-info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['business:software-info:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="完整名称" align="center" prop="softwareFullName" />
      <el-table-column label="软著登记类型" align="center" prop="softwareType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_TYPE" :value="scope.row.softwareType" />
        </template>
      </el-table-column>
      <el-table-column label="取得方式" align="center" prop="acquisitionMethod">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_ACQUISITION_METHOD" :value="scope.row.acquisitionMethod" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="版本编号" align="center" prop="versionNumber" /> -->
      <!-- <el-table-column label="权利范围" align="center" prop="rightsScope">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_RIGHTS_SCOPE" :value="scope.row.rightsScope" />
        </template>
      </el-table-column> -->
      <el-table-column label="平台分类" align="center" prop="platformType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_PLATFORM_TYPE" :value="scope.row.platformType" />
        </template>
      </el-table-column>
      <el-table-column label="软件分类" align="center" prop="softwareClassification">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_CLASSIFICATION" :value="scope.row.softwareClassification" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="软件说明" align="center" prop="softwareDescription">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_DESCRIPTION" :value="scope.row.softwareDescription" />
        </template>
      </el-table-column> -->
      <el-table-column label="开发方式" align="center" prop="developmentMethod">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_DEVELOPMENT_METHOD" :value="scope.row.developmentMethod" />
        </template>
      </el-table-column>
      <el-table-column label="编程语言" align="center" prop="programmingLanguages">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_PROGRAMMING_LANGUAGES" :value="scope.row.programmingLanguages" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="编程语言描述" align="center" prop="programmingLanguagesDescribe" />
      <el-table-column label="开发完成日期" align="center" prop="completionDate" />
      <el-table-column label="登记人员邮箱" align="center" prop="registrantEmail" />
      <el-table-column label="登记人员手机号" align="center" prop="registrantPhone" />
      <el-table-column label="详细介绍内容" align="center" prop="softwareIntroduction" />
      <el-table-column label="软著发表状态" align="center" prop="publishStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_PUBLISH_STATUS" :value="scope.row.publishStatus" />
        </template>
      </el-table-column>
      <el-table-column label="开发的硬件环境" align="center" prop="developmentHardwareEnvironment" />
      <el-table-column label="运行的硬件环境" align="center" prop="runningHardwareEnvironment" />
      <el-table-column label="开发该软件的操作系统" align="center" prop="developmentOs" />
      <el-table-column label="软件开发环境/开发工具" align="center" prop="developmentTools" />
      <el-table-column label="该软件的运行平台/操作系统" align="center" prop="runningPlatformOs" />
      <el-table-column label="软件运行支撑环境/支持软件" align="center" prop="runningSupportEnvironment" />
      <el-table-column label="源程序的行数" align="center" prop="sourceCodeLines" />
      <el-table-column label="开发目的" align="center" prop="developmentPurpose" />
      <el-table-column label="面向领域/行业" align="center" prop="targetDomain" />
      <el-table-column label="软件的主要功能" align="center" prop="mainFunction" />
      <el-table-column label="软件的技术特点，多种特点以逗号分隔" align="center" prop="technicalFeatures">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_TECHNICAL_FEATURES" :value="scope.row.technicalFeatures" />
        </template>
      </el-table-column>
      <el-table-column label="软件技术特点描述" align="center" prop="technicalFeaturesDescribe" />
      <el-table-column label="程序鉴别材料的提交方式" align="center" prop="programIdentificationType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_DEPOSIT_TYPE" :value="scope.row.programIdentificationType" />
        </template>
      </el-table-column>
      <el-table-column label="程序鉴别材料ID，多个以逗号隔开" align="center" prop="programIdentificationIds" />
      <el-table-column label="文档鉴别材料的提交方式" align="center" prop="documentIdentificationType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOFTWARE_DEPOSIT_TYPE" :value="scope.row.documentIdentificationType" />
        </template>
      </el-table-column>
      <el-table-column label="文档鉴别材料ID，多个以逗号隔开" align="center" prop="documentIdentificationIds" />
      <el-table-column label="其他相关证明文件ID，多个以逗号隔开" align="center" prop="otherCertificationIds" /> -->
      <el-table-column label="状态" align="center" prop="statusCd" />
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['business:software-info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['business:software-info:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SoftwareInfoForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { SoftwareInfoApi, SoftwareInfoVO } from '@/api/business/software'
import SoftwareInfoForm from './SoftwareRegistrationInfoForm.vue'

/** 软件登记信息 列表 */
defineOptions({ name: 'SoftwareInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<SoftwareInfoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  softwareType: undefined,
  acquisitionMethod: undefined,
  softwareFullName: undefined,
  versionNumber: undefined,
  rightsScope: undefined,
  platformType: undefined,
  softwareClassification: undefined,
  softwareDescription: undefined,
  developmentMethod: undefined,
  programmingLanguages: undefined,
  programmingLanguagesDescribe: undefined,
  completionDate: [],
  registrantEmail: undefined,
  registrantPhone: undefined,
  softwareIntroduction: undefined,
  publishStatus: undefined,
  developmentHardwareEnvironment: undefined,
  runningHardwareEnvironment: undefined,
  developmentOs: undefined,
  developmentTools: undefined,
  runningPlatformOs: undefined,
  runningSupportEnvironment: undefined,
  sourceCodeLines: undefined,
  developmentPurpose: undefined,
  targetDomain: undefined,
  mainFunction: undefined,
  technicalFeatures: undefined,
  technicalFeaturesDescribe: undefined,
  programIdentificationType: undefined,
  programIdentificationIds: undefined,
  documentIdentificationType: undefined,
  documentIdentificationIds: undefined,
  otherCertificationIds: undefined,
  statusCd: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await SoftwareInfoApi.getSoftwareInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SoftwareInfoApi.deleteSoftwareInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SoftwareInfoApi.exportSoftwareInfo(queryParams)
    download.excel(data, '软件登记信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>